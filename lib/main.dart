import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:logger/logger.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'core/service_locator.dart';
import 'core/theme/ux_theme_config.dart';
import 'services/theme_service.dart';
import 'services/analytics_service.dart';
import 'presentation/pages/splash_page.dart';
import 'presentation/pages/user_selection_page.dart';
import 'presentation/pages/user_creation_page.dart';
import 'presentation/pages/home_page.dart';
import 'presentation/pages/settings_page.dart';
import 'presentation/pages/theme_demo_page.dart';

/// 应用入口点
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  final logger = Logger();
  logger.i('LogicLab app starting...');

  try {
    // 初始化Hive数据库
    await Hive.initFlutter();
    logger.i('Hive database initialized');

    // 设置服务定位器
    await setupServiceLocator();
    logger.i('Service locator configured');

    // 设置系统UI样式
    await _setupSystemUI();
    logger.i('System UI configured');

    // 启动应用
    runApp(const LogicLabApp());
    logger.i('LogicLab app launched successfully');
  } catch (e, stackTrace) {
    logger.e('Failed to start LogicLab app', error: e, stackTrace: stackTrace);

    // 显示错误页面
    runApp(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                const Text(
                  '应用启动失败',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Text(
                  '错误信息: $e',
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 14, color: Colors.grey),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 设置系统UI样式
Future<void> _setupSystemUI() async {
  // 获取当前主题模式
  final brightness =
      WidgetsBinding.instance.platformDispatcher.platformBrightness;
  final isDark = brightness == Brightness.dark;

  // 设置系统UI覆盖样式
  SystemChrome.setSystemUIOverlayStyle(
    SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
      statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
      systemNavigationBarColor: isDark
          ? UXThemeConfig.textDark
          : UXThemeConfig.backgroundPrimary,
      systemNavigationBarIconBrightness: isDark
          ? Brightness.light
          : Brightness.dark,
      systemNavigationBarDividerColor: isDark
          ? UXThemeConfig.textSecondary
          : UXThemeConfig.borderPrimary,
    ),
  );
}

/// LogicLab主应用
class LogicLabApp extends StatefulWidget {
  const LogicLabApp({super.key});

  @override
  State<LogicLabApp> createState() => _LogicLabAppState();
}

class _LogicLabAppState extends State<LogicLabApp> with WidgetsBindingObserver {
  final Logger _logger = Logger();
  late ThemeService _themeService;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _themeService = sl<ThemeService>();
    _themeService.addListener(_onThemeChanged);
    _logger.i('LogicLab app initialized with ThemeService integration');
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _themeService.removeListener(_onThemeChanged);
    super.dispose();
  }

  @override
  void didChangePlatformBrightness() {
    super.didChangePlatformBrightness();
    // 当系统主题模式改变时，更新系统UI样式
    _setupSystemUI();
    _logger.d('Platform brightness changed, updating system UI');
  }

  /// 主题变化回调
  void _onThemeChanged() {
    if (mounted) {
      setState(() {});
      _logger.d('Theme changed, rebuilding app with new theme');
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _themeService,
      builder: (context, child) {
        return MaterialApp(
          title: 'LogicLab - 逻辑实验室',
          debugShowCheckedModeBanner: false,

          // 应用ThemeService管理的主题
          theme: _themeService.getLightTheme(),
          darkTheme: _themeService.getDarkTheme(),
          themeMode: _themeService.themeMode,

          // 主题动画配置
          themeAnimationDuration: _themeService.animatedTransitions
              ? const Duration(milliseconds: 300)
              : Duration.zero,
          themeAnimationCurve: Curves.easeInOut,

          // 本地化配置
          locale: const Locale('zh', 'CN'),
          supportedLocales: const [
            Locale('zh', 'CN'), // 中文
            Locale('en', 'US'), // 英文（备用）
          ],

          // 首页
          home: const SplashPage(),

          // 路由配置
          routes: {
            '/splash': (context) => const SplashPage(),
            '/user_selection': (context) => const UserSelectionPage(),
            '/user_creation': (context) => const UserCreationPage(),
            '/home': (context) => const HomePage(),
            '/settings': (context) => const SettingsPage(),
            '/theme_demo': (context) => const ThemeDemoPage(),
            '/puzzle_game': (context) => const _PlaceholderPage(title: '谜题游戏'),
            '/world_map': (context) => const _PlaceholderPage(title: '世界地图'),
            '/puzzle_list': (context) => const _PlaceholderPage(title: '谜题列表'),
            '/achievements': (context) => const _PlaceholderPage(title: '成就系统'),
          },

          // 全局导航器观察者
          navigatorObservers: [_LogicLabNavigatorObserver()],

          // 构建器 - 用于全局错误处理和主题适配
          builder: (context, child) {
            return MediaQuery(
              // 禁用系统字体缩放，确保UI一致性
              data: MediaQuery.of(
                context,
              ).copyWith(textScaler: const TextScaler.linear(1.0)),
              child: _ResponsiveWrapper(child: child!),
            );
          },
        );
      },
    );
  }
}

/// 设备类型枚举
enum DeviceType { mobile, tablet, desktop, large }

/// 响应式包装器
///
/// 为应用提供响应式设计支持和全局错误处理
class _ResponsiveWrapper extends StatelessWidget {
  final Widget child;

  const _ResponsiveWrapper({required this.child});

  /// 根据屏幕宽度获取设备类型
  DeviceType _getDeviceType(double width) {
    if (width < 600) return DeviceType.mobile;
    if (width < 900) return DeviceType.tablet;
    if (width < 1200) return DeviceType.desktop;
    return DeviceType.large;
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final deviceType = _getDeviceType(screenWidth);

    // 根据设备类型调整布局
    return Container(
      // 为大屏设备添加最大宽度限制
      constraints:
          deviceType == DeviceType.desktop || deviceType == DeviceType.large
          ? const BoxConstraints(
              maxWidth: 1200, // 桌面最大宽度
            )
          : null,
      child: Center(child: ClipRect(child: child)),
    );
  }
}

/// 占位页面组件
///
/// 用于尚未实现的页面，提供一致的UI体验
class _PlaceholderPage extends StatelessWidget {
  final String title;

  const _PlaceholderPage({required this.title});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        backgroundColor: UXThemeConfig.primaryBlue,
        foregroundColor: Colors.white,
        actions: [
          // 添加主题演示按钮
          IconButton(
            icon: const Icon(Icons.palette),
            onPressed: () => Navigator.pushNamed(context, '/theme_demo'),
            tooltip: '主题演示',
          ),
        ],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              UXThemeConfig.backgroundPrimary,
              UXThemeConfig.backgroundPrimary,
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 占位图标
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: UXThemeConfig.primaryBlue,
                  borderRadius: BorderRadius.circular(UXThemeConfig.radiusXL),
                  boxShadow: [
                    BoxShadow(
                      color: UXThemeConfig.shadowMedium,
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Icon(Icons.construction, size: 48, color: Colors.white),
              ),

              SizedBox(height: UXThemeConfig.paddingXL),

              // 标题
              Text(
                title,
                style: TextStyle(
                  fontSize: UXThemeConfig.fontSizeL,
                  fontWeight: FontWeight.bold,
                  color: UXThemeConfig.textDark,
                ),
              ),

              SizedBox(height: UXThemeConfig.paddingM),

              // 描述
              Text(
                '该功能正在开发中\n敬请期待！',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: UXThemeConfig.fontSizeBody,
                  color: UXThemeConfig.textSecondary,
                  height: 1.5,
                ),
              ),

              SizedBox(height: UXThemeConfig.paddingXL * 2),

              // 操作按钮
              Wrap(
                spacing: UXThemeConfig.paddingM,
                children: [
                  // 主题演示按钮
                  ElevatedButton.icon(
                    onPressed: () =>
                        Navigator.pushNamed(context, '/theme_demo'),
                    icon: const Icon(Icons.palette, size: 20),
                    label: const Text('主题演示'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: UXThemeConfig.accentBlue,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(
                        horizontal: UXThemeConfig.paddingL,
                        vertical: UXThemeConfig.paddingM,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          UXThemeConfig.radiusM,
                        ),
                      ),
                    ),
                  ),

                  // 返回按钮
                  ElevatedButton.icon(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.arrow_back, size: 20),
                    label: const Text('返回'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: UXThemeConfig.primaryBlue,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(
                        horizontal: UXThemeConfig.paddingL,
                        vertical: UXThemeConfig.paddingM,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          UXThemeConfig.radiusM,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 导航观察者 - 用于跟踪页面导航和用户行为
class _LogicLabNavigatorObserver extends NavigatorObserver {
  final Logger _logger = Logger();

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    _logger.d('Navigated to: ${route.settings.name ?? 'Unknown'}');

    // 记录页面访问统计
    _trackPageView(route.settings.name);
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    _logger.d('Navigated back from: ${route.settings.name ?? 'Unknown'}');
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    _logger.d(
      'Replaced route: ${oldRoute?.settings.name ?? 'Unknown'} -> ${newRoute?.settings.name ?? 'Unknown'}',
    );

    // 记录页面替换统计
    _trackPageView(newRoute?.settings.name);
  }

  @override
  void didRemove(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didRemove(route, previousRoute);
    _logger.d('Removed route: ${route.settings.name ?? 'Unknown'}');
  }

  /// 记录页面访问统计
  void _trackPageView(String? routeName) {
    if (routeName != null) {
      // 实现页面访问统计
      try {
        final analyticsService = sl<AnalyticsService>();
        analyticsService.trackPageView(routeName);
        _logger.i('Page view tracked: $routeName');
      } catch (e) {
        _logger.w('Failed to track page view: $e');
      }
    }
  }
}
