import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:logger/logger.dart';

import '../domain/repositories/user_repository.dart';
import '../domain/entities/user_profile_entity.dart';

/// 数据导出服务
///
/// 负责导出用户数据，包括：
/// - 游戏进度
/// - 成就记录
/// - 统计数据
/// - 设置信息
class DataExportService {
  static final DataExportService _instance = DataExportService._internal();
  factory DataExportService() => _instance;
  DataExportService._internal();

  final Logger _logger = Logger();

  /// 导出用户数据
  Future<bool> exportUserData(UserRepository userRepository) async {
    try {
      _logger.i('Starting user data export...');

      // 获取用户数据
      final userResult = await userRepository.getCurrentUser();
      if (!userResult.isSuccess || userResult.data == null) {
        _logger.e('Failed to get user data for export');
        return false;
      }

      final user = userResult.data!;

      // 构建导出数据
      final exportData = await _buildExportData(user);

      // 生成文件
      final file = await _createExportFile(exportData);

      // 保存文件（简化版本，不使用分享功能）
      _logger.i('Export file saved to: ${file.path}');

      _logger.i('User data export completed successfully');
      return true;
    } catch (e, stackTrace) {
      _logger.e('Failed to export user data', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// 构建导出数据
  Future<Map<String, dynamic>> _buildExportData(UserProfileEntity user) async {
    final exportData = <String, dynamic>{
      'export_info': {
        'version': '1.0',
        'timestamp': DateTime.now().toIso8601String(),
        'app_version': '1.0.0', // TODO: 从包信息获取
      },
      'user_profile': {
        'id': user.id,
        'nickname': user.nickname,
        'avatar_id': user.avatarId,
        'created_at': user.createdAt.toIso8601String(),
        'last_played_at': user.lastPlayedAt.toIso8601String(),
      },
      'game_progress': {
        'total_score': user.totalScore,
        'total_stars': user.totalStars,
        'total_points': user.totalPoints,
        'total_play_time_minutes': user.totalPlayTimeMinutes,
        'level_progress': _convertLevelProgress(user.levelProgress),
      },
      'skills': {
        'skill_points': user.skillPoints,
        'skill_levels': user.skillPoints.map(
          (skill, points) => MapEntry(skill, (points ~/ 100) + 1),
        ),
      },
      'achievements': {
        'unlocked_achievements': user.unlockedAchievements,
        // achievement_progress 字段在当前实体中不存在，暂时省略
      },
      'statistics': await _calculateStatistics(user),
      'settings': {
        'user_settings': {
          // 从user.settings获取设置信息
          'settings_data': 'Available in user.settings',
        },
        // 可以添加更多设置信息
      },
    };

    return exportData;
  }

  /// 转换关卡进度数据
  Map<String, dynamic> _convertLevelProgress(
    Map<String, LevelProgressEntity> levelProgress,
  ) {
    return levelProgress.map(
      (levelId, progress) => MapEntry(levelId, {
        'completed': progress.completed,
        'best_score': progress.bestScore,
        'best_time_seconds': progress.bestTimeSeconds,
        'star_rating': progress.starRating,
        'attempts': progress.attempts,
        'hints_used': progress.hintsUsed,
        'first_completed_at': progress.firstCompletedAt?.toIso8601String(),
        'last_attempt_at': progress.lastAttemptAt?.toIso8601String(),
      }),
    );
  }

  /// 计算统计数据
  Future<Map<String, dynamic>> _calculateStatistics(
    UserProfileEntity user,
  ) async {
    final completedLevels = user.levelProgress.values
        .where((progress) => progress.completed)
        .toList();

    final totalAttempts = user.levelProgress.values
        .map((progress) => progress.attempts)
        .fold(0, (sum, attempts) => sum + attempts);

    final totalHintsUsed = user.levelProgress.values
        .map((progress) => progress.hintsUsed)
        .fold(0, (sum, hints) => sum + hints);

    final averageScore = completedLevels.isEmpty
        ? 0.0
        : completedLevels
                  .map((progress) => progress.bestScore)
                  .reduce((a, b) => a + b) /
              completedLevels.length;

    final fastestTime = completedLevels.isEmpty
        ? 0
        : completedLevels
              .map((progress) => progress.bestTimeSeconds)
              .reduce((a, b) => a < b ? a : b);

    return {
      'total_levels_completed': completedLevels.length,
      'total_attempts': totalAttempts,
      'total_hints_used': totalHintsUsed,
      'average_score': averageScore,
      'fastest_completion_time': fastestTime,
      'completion_rate': user.levelProgress.isEmpty
          ? 0.0
          : completedLevels.length / user.levelProgress.length,
      'perfect_scores': completedLevels
          .where((progress) => progress.bestScore >= 100)
          .length,
      'no_hint_completions': completedLevels
          .where((progress) => progress.hintsUsed == 0)
          .length,
    };
  }

  /// 创建导出文件
  Future<File> _createExportFile(Map<String, dynamic> exportData) async {
    final documentsDir = await getApplicationDocumentsDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final fileName = 'logiclab_data_export_$timestamp.json';
    final file = File('${documentsDir.path}/$fileName');

    // 格式化JSON输出
    const encoder = JsonEncoder.withIndent('  ');
    final jsonString = encoder.convert(exportData);

    await file.writeAsString(jsonString);

    _logger.d('Export file created: ${file.path}');
    return file;
  }

  /// 获取导出文件路径（供用户查看）
  String getExportFilePath(File file) {
    return file.path;
  }

  /// 导出特定类型的数据
  Future<bool> exportSpecificData({
    required UserRepository userRepository,
    required String dataType,
  }) async {
    try {
      final userResult = await userRepository.getCurrentUser();
      if (!userResult.isSuccess || userResult.data == null) {
        return false;
      }

      final user = userResult.data!;
      Map<String, dynamic> exportData;

      switch (dataType) {
        case 'progress':
          exportData = {
            'game_progress': {
              'level_progress': _convertLevelProgress(user.levelProgress),
              'total_score': user.totalScore,
              'total_stars': user.totalStars,
            },
          };
          break;
        case 'achievements':
          exportData = {
            'achievements': {
              'unlocked_achievements': user.unlockedAchievements,
              // achievement_progress 字段在当前实体中不存在
            },
          };
          break;
        case 'statistics':
          exportData = {'statistics': await _calculateStatistics(user)};
          break;
        default:
          return await exportUserData(userRepository);
      }

      final file = await _createExportFile(exportData);
      _logger.i('Specific data export file saved to: ${file.path}');

      return true;
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to export specific data',
        error: e,
        stackTrace: stackTrace,
      );
      return false;
    }
  }
}
