import 'package:logger/logger.dart';

import '../core/exceptions/app_exceptions.dart';

/// 成就信息数据类
class AchievementInfo {
  final String id;
  final String title;
  final String description;
  final String iconPath;
  final AchievementRarity rarity;
  final int rewardPoints;
  final DateTime? unlockedAt;
  final String? category;
  final List<String> requirements;

  const AchievementInfo({
    required this.id,
    required this.title,
    required this.description,
    required this.iconPath,
    required this.rarity,
    required this.rewardPoints,
    this.unlockedAt,
    this.category,
    this.requirements = const [],
  });

  /// 复制并更新解锁时间
  AchievementInfo copyWith({
    DateTime? unlockedAt,
    String? category,
    List<String>? requirements,
  }) {
    return AchievementInfo(
      id: id,
      title: title,
      description: description,
      iconPath: iconPath,
      rarity: rarity,
      rewardPoints: rewardPoints,
      unlockedAt: unlockedAt ?? this.unlockedAt,
      category: category ?? this.category,
      requirements: requirements ?? this.requirements,
    );
  }

  /// 是否已解锁
  bool get isUnlocked => unlockedAt != null;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AchievementInfo &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// 成就稀有度枚举
enum AchievementRarity {
  common('普通', 1.0),
  uncommon('常见', 1.2),
  rare('稀有', 1.5),
  epic('史诗', 2.0),
  legendary('传奇', 3.0);

  const AchievementRarity(this.displayName, this.multiplier);

  final String displayName;
  final double multiplier;
}

/// 成就服务 - 管理成就数据和业务逻辑
class AchievementService {
  final Logger _logger = Logger();
  
  // 预定义的成就列表
  static final List<AchievementInfo> _allAchievements = [
    // 基础成就
    AchievementInfo(
      id: 'first_puzzle',
      title: '初学者',
      description: '完成第一个谜题',
      iconPath: 'assets/images/achievements/first_puzzle.png',
      rarity: AchievementRarity.common,
      rewardPoints: 10,
      category: 'basic',
      requirements: ['complete_any_puzzle'],
    ),
    AchievementInfo(
      id: 'puzzle_master_10',
      title: '谜题新手',
      description: '累计完成10个谜题',
      iconPath: 'assets/images/achievements/puzzle_master_10.png',
      rarity: AchievementRarity.common,
      rewardPoints: 50,
      category: 'progress',
      requirements: ['complete_10_puzzles'],
    ),
    AchievementInfo(
      id: 'puzzle_master_50',
      title: '谜题专家',
      description: '累计完成50个谜题',
      iconPath: 'assets/images/achievements/puzzle_master_50.png',
      rarity: AchievementRarity.uncommon,
      rewardPoints: 100,
      category: 'progress',
      requirements: ['complete_50_puzzles'],
    ),
    
    // 速度成就
    AchievementInfo(
      id: 'speed_master',
      title: '速度大师',
      description: '在30秒内完成一个谜题',
      iconPath: 'assets/images/achievements/speed_master.png',
      rarity: AchievementRarity.rare,
      rewardPoints: 25,
      category: 'speed',
      requirements: ['complete_puzzle_under_30s'],
    ),
    AchievementInfo(
      id: 'lightning_fast',
      title: '闪电侠',
      description: '在15秒内完成一个谜题',
      iconPath: 'assets/images/achievements/lightning_fast.png',
      rarity: AchievementRarity.epic,
      rewardPoints: 50,
      category: 'speed',
      requirements: ['complete_puzzle_under_15s'],
    ),
    
    // 技能成就
    AchievementInfo(
      id: 'pattern_expert',
      title: '图形推理专家',
      description: '图形推理技能达到5级',
      iconPath: 'assets/images/achievements/pattern_expert.png',
      rarity: AchievementRarity.uncommon,
      rewardPoints: 75,
      category: 'skill',
      requirements: ['pattern_skill_level_5'],
    ),
    AchievementInfo(
      id: 'spatial_master',
      title: '空间想象大师',
      description: '空间想象技能达到5级',
      iconPath: 'assets/images/achievements/spatial_master.png',
      rarity: AchievementRarity.uncommon,
      rewardPoints: 75,
      category: 'skill',
      requirements: ['spatial_skill_level_5'],
    ),
    AchievementInfo(
      id: 'logic_genius',
      title: '逻辑天才',
      description: '数字逻辑技能达到5级',
      iconPath: 'assets/images/achievements/logic_genius.png',
      rarity: AchievementRarity.uncommon,
      rewardPoints: 75,
      category: 'skill',
      requirements: ['numeric_skill_level_5'],
    ),
    AchievementInfo(
      id: 'coding_prodigy',
      title: '编程神童',
      description: '编程启蒙技能达到5级',
      iconPath: 'assets/images/achievements/coding_prodigy.png',
      rarity: AchievementRarity.uncommon,
      rewardPoints: 75,
      category: 'skill',
      requirements: ['coding_skill_level_5'],
    ),
    
    // 特殊成就
    AchievementInfo(
      id: 'perfectionist',
      title: '完美主义者',
      description: '连续10次获得3星评分',
      iconPath: 'assets/images/achievements/perfectionist.png',
      rarity: AchievementRarity.rare,
      rewardPoints: 100,
      category: 'special',
      requirements: ['consecutive_3_stars_10'],
    ),
    AchievementInfo(
      id: 'no_hints_master',
      title: '独立思考者',
      description: '不使用提示完成20个谜题',
      iconPath: 'assets/images/achievements/no_hints_master.png',
      rarity: AchievementRarity.epic,
      rewardPoints: 150,
      category: 'special',
      requirements: ['no_hints_20_puzzles'],
    ),
    AchievementInfo(
      id: 'ultimate_champion',
      title: '终极冠军',
      description: '完成所有类型的谜题并达到最高等级',
      iconPath: 'assets/images/achievements/ultimate_champion.png',
      rarity: AchievementRarity.legendary,
      rewardPoints: 500,
      category: 'legendary',
      requirements: ['all_skills_max_level', 'complete_all_puzzle_types'],
    ),
  ];

  /// 获取所有成就
  List<AchievementInfo> getAllAchievements() {
    return List.unmodifiable(_allAchievements);
  }

  /// 根据ID获取成就
  AchievementInfo? getAchievementById(String achievementId) {
    try {
      return _allAchievements.firstWhere((achievement) => achievement.id == achievementId);
    } catch (e) {
      _logger.w('Achievement not found: $achievementId');
      return null;
    }
  }

  /// 根据类别获取成就
  List<AchievementInfo> getAchievementsByCategory(String category) {
    return _allAchievements.where((achievement) => achievement.category == category).toList();
  }

  /// 根据稀有度获取成就
  List<AchievementInfo> getAchievementsByRarity(AchievementRarity rarity) {
    return _allAchievements.where((achievement) => achievement.rarity == rarity).toList();
  }

  /// 获取用户已解锁的成就
  List<AchievementInfo> getUserUnlockedAchievements(List<String> unlockedAchievementIds) {
    return _allAchievements
        .where((achievement) => unlockedAchievementIds.contains(achievement.id))
        .toList();
  }

  /// 获取用户未解锁的成就
  List<AchievementInfo> getUserLockedAchievements(List<String> unlockedAchievementIds) {
    return _allAchievements
        .where((achievement) => !unlockedAchievementIds.contains(achievement.id))
        .toList();
  }

  /// 检查用户是否可以解锁某个成就
  bool canUnlockAchievement(String achievementId, Map<String, dynamic> userStats) {
    final achievement = getAchievementById(achievementId);
    if (achievement == null) return false;

    // TODO: 实现具体的解锁条件检查逻辑
    // 这里可以根据achievement.requirements和userStats来判断
    return true; // 暂时返回true
  }

  /// 获取用户可解锁的成就列表
  List<String> getUnlockableAchievements(
    List<String> unlockedAchievementIds,
    Map<String, dynamic> userStats,
  ) {
    final lockedAchievements = getUserLockedAchievements(unlockedAchievementIds);
    return lockedAchievements
        .where((achievement) => canUnlockAchievement(achievement.id, userStats))
        .map((achievement) => achievement.id)
        .toList();
  }

  /// 计算成就完成百分比
  double calculateCompletionPercentage(List<String> unlockedAchievementIds) {
    if (_allAchievements.isEmpty) return 0.0;
    return unlockedAchievementIds.length / _allAchievements.length;
  }

  /// 计算总成就点数
  int calculateTotalAchievementPoints(List<String> unlockedAchievementIds) {
    return getUserUnlockedAchievements(unlockedAchievementIds)
        .fold(0, (total, achievement) => total + achievement.rewardPoints);
  }

  /// 验证成就ID是否存在
  void validateAchievementId(String achievementId) {
    if (getAchievementById(achievementId) == null) {
      throw AchievementNotFoundException(achievementId: achievementId);
    }
  }

  /// 关闭服务
  void dispose() {
    _logger.i('AchievementService disposed');
  }
} 