import 'package:hive_flutter/hive_flutter.dart';
import 'package:logger/logger.dart';
import '../data/models/user_profile.dart';
import '../core/constants/app_constants.dart';
import '../core/exceptions/app_exceptions.dart';
import '../core/utils/skill_type_mapper.dart';

/// 用户服务 - 处理用户档案的本地存储和管理
class UserService {
  static final UserService _instance = UserService._internal();
  factory UserService() => _instance;
  UserService._internal();

  late Box<UserProfile> _userBox;
  late Box<String> _settingsBox;
  final Logger _logger = Logger();

  /// 初始化用户服务
  Future<void> initialize() async {
    try {
      await Hive.initFlutter();
      
      // 注册适配器
      if (!Hive.isAdapterRegistered(0)) {
        Hive.registerAdapter(UserProfileAdapter());
      }
      if (!Hive.isAdapterRegistered(1)) {
        Hive.registerAdapter(LevelProgressAdapter());
      }
      if (!Hive.isAdapterRegistered(2)) {
        Hive.registerAdapter(UserSettingsAdapter());
      }

      // 打开数据库
      _userBox = await Hive.openBox<UserProfile>(AppConstants.userProfileBoxName);
      _settingsBox = await Hive.openBox<String>(AppConstants.settingsBoxName);
      
      _logger.i('UserService initialized successfully');
    } catch (e, stackTrace) {
      _logger.e('Failed to initialize UserService', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 创建新用户档案
  Future<UserProfile> createUser({
    required String nickname,
    required String avatarId,
  }) async {
    try {
      // 验证用户数量限制
      if (_userBox.length >= AppConstants.maxUsers) {
        throw Exception('已达到最大用户数量限制');
      }

      // 验证昵称长度
      if (nickname.length > AppConstants.maxNicknameLength) {
        throw Exception('昵称长度不能超过${AppConstants.maxNicknameLength}个字符');
      }

      // 检查昵称是否已存在
      if (await isNicknameExists(nickname)) {
        throw Exception('昵称已存在');
      }

      // 创建新用户
      final user = UserProfile.create(
        nickname: nickname,
        avatarId: avatarId,
      );

      // 保存到数据库
      await _userBox.put(user.id, user);
      
      // 设置为当前用户
      await setCurrentUser(user.id);

      _logger.i('Created new user: ${user.nickname} (${user.id})');
      return user;
    } catch (e, stackTrace) {
      _logger.e('Failed to create user', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 获取所有用户档案
  List<UserProfile> getAllUsers() {
    try {
      final users = _userBox.values.toList();
      // 按最后游戏时间排序
      users.sort((a, b) => b.lastPlayedAt.compareTo(a.lastPlayedAt));
      return users;
    } catch (e, stackTrace) {
      _logger.e('Failed to get all users', error: e, stackTrace: stackTrace);
      return [];
    }
  }

  /// 根据ID获取用户档案
  UserProfile? getUserById(String userId) {
    try {
      return _userBox.get(userId);
    } catch (e, stackTrace) {
      _logger.e('Failed to get user by ID: $userId', error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// 更新用户档案
  Future<void> updateUser(UserProfile user) async {
    try {
      await _userBox.put(user.id, user);
      _logger.i('Updated user: ${user.nickname} (${user.id})');
    } catch (e, stackTrace) {
      _logger.e('Failed to update user', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 删除用户档案
  Future<void> deleteUser(String userId) async {
    try {
      await _userBox.delete(userId);
      
      // 如果删除的是当前用户，清除当前用户设置
      final currentUserId = await getCurrentUserId();
      if (currentUserId == userId) {
        await _settingsBox.delete('current_user_id');
      }
      
      _logger.i('Deleted user: $userId');
    } catch (e, stackTrace) {
      _logger.e('Failed to delete user: $userId', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 检查昵称是否已存在
  Future<bool> isNicknameExists(String nickname) async {
    try {
      return _userBox.values.any((user) => user.nickname == nickname);
    } catch (e, stackTrace) {
      _logger.e('Failed to check nickname existence', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// 根据昵称获取用户档案
  UserProfile? getUserByNickname(String nickname) {
    try {
      final users = _userBox.values.where((user) => user.nickname == nickname);
      return users.isNotEmpty ? users.first : null;
    } catch (e, stackTrace) {
      _logger.e('Failed to get user by nickname: $nickname', error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// 获取用户总数
  int getUserCount() {
    try {
      return _userBox.length;
    } catch (e, stackTrace) {
      _logger.e('Failed to get user count', error: e, stackTrace: stackTrace);
      return 0;
    }
  }

  /// 搜索用户
  List<UserProfile> searchUsers({
    String? nickname,
    String? avatarId,
    int? minLevel,
    int? maxLevel,
  }) {
    try {
      var users = _userBox.values.toList();
      
      if (nickname != null && nickname.isNotEmpty) {
        users = users.where((user) => 
          user.nickname.toLowerCase().contains(nickname.toLowerCase())
        ).toList();
      }
      
      if (avatarId != null && avatarId.isNotEmpty) {
        users = users.where((user) => user.avatarId == avatarId).toList();
      }
      
      if (minLevel != null || maxLevel != null) {
        users = users.where((user) {
          final userLevel = _calculateUserLevel(user);
          if (minLevel != null && userLevel < minLevel) return false;
          if (maxLevel != null && userLevel > maxLevel) return false;
          return true;
        }).toList();
      }
      
      return users;
    } catch (e, stackTrace) {
      _logger.e('Failed to search users', error: e, stackTrace: stackTrace);
      return [];
    }
  }

  /// 计算用户等级
  int _calculateUserLevel(UserProfile user) {
    final totalSkillPoints = user.skillPoints.values.fold(
      0,
      (sum, points) => sum + points,
    );
    return (totalSkillPoints / 100).floor();
  }

  /// 设置当前用户
  Future<void> setCurrentUser(String userId) async {
    try {
      await _settingsBox.put('current_user_id', userId);
      
      // 更新用户的最后游戏时间
      final user = getUserById(userId);
      if (user != null) {
        final updatedUser = user.copyWith(lastPlayedAt: DateTime.now());
        await updateUser(updatedUser);
      }
      
      _logger.i('Set current user: $userId');
    } catch (e, stackTrace) {
      _logger.e('Failed to set current user: $userId', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 获取当前用户ID
  Future<String?> getCurrentUserId() async {
    try {
      return _settingsBox.get('current_user_id');
    } catch (e, stackTrace) {
      _logger.e('Failed to get current user ID', error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// 获取当前用户档案
  Future<UserProfile?> getCurrentUser() async {
    try {
      final userId = await getCurrentUserId();
      if (userId == null) return null;
      return getUserById(userId);
    } catch (e, stackTrace) {
      _logger.e('Failed to get current user', error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// 更新用户关卡进度
  Future<void> updateLevelProgress({
    required String userId,
    required String levelId,
    required int score,
    required int timeSeconds,
    required int hintsUsed,
  }) async {
    try {
      final user = getUserById(userId);
      if (user == null) {
        throw UserNotFoundException(userId: userId);
      }

      // 获取或创建关卡进度
      final currentProgress = user.levelProgress[levelId] ?? LevelProgress.create(levelId);
      
      // 更新进度
      final updatedProgress = currentProgress.updateProgress(
        score: score,
        timeSeconds: timeSeconds,
        hintsUsed: hintsUsed,
      );

      // 更新用户档案
      final updatedLevelProgress = Map<String, LevelProgress>.from(user.levelProgress);
      updatedLevelProgress[levelId] = updatedProgress;

      // 更新技能点数
      final updatedSkillPoints = Map<String, int>.from(user.skillPoints);
      final skillType = SkillTypeMapper.getSkillTypeFromLevelId(levelId);
      updatedSkillPoints[skillType] = (updatedSkillPoints[skillType] ?? 0) + score * 10;

      final updatedUser = user.copyWith(
        levelProgress: updatedLevelProgress,
        skillPoints: updatedSkillPoints,
        lastPlayedAt: DateTime.now(),
      );

      await updateUser(updatedUser);
      _logger.i('Updated level progress for user $userId, level $levelId');
    } catch (e, stackTrace) {
      _logger.e('Failed to update level progress', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 解锁成就
  Future<void> unlockAchievement({
    required String userId,
    required String achievementId,
  }) async {
    try {
      final user = getUserById(userId);
      if (user == null) {
        throw UserNotFoundException(userId: userId);
      }

      if (user.unlockedAchievements.contains(achievementId)) {
        return; // 已经解锁
      }

      final updatedAchievements = List<String>.from(user.unlockedAchievements);
      updatedAchievements.add(achievementId);

      final updatedUser = user.copyWith(
        unlockedAchievements: updatedAchievements,
      );

      await updateUser(updatedUser);
      _logger.i('Unlocked achievement $achievementId for user $userId');
    } catch (e, stackTrace) {
      _logger.e('Failed to unlock achievement', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 更新用户设置
  Future<void> updateUserSettings({
    required String userId,
    required UserSettings settings,
  }) async {
    try {
      final user = getUserById(userId);
      if (user == null) {
        throw UserNotFoundException(userId: userId);
      }

      final updatedUser = user.copyWith(settings: settings);
      await updateUser(updatedUser);
      
      _logger.i('Updated settings for user $userId');
    } catch (e, stackTrace) {
      _logger.e('Failed to update user settings', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 增加游戏时间
  Future<void> addPlayTime({
    required String userId,
    required int minutes,
  }) async {
    try {
      final user = getUserById(userId);
      if (user == null) {
        throw UserNotFoundException(userId: userId);
      }

      final updatedUser = user.copyWith(
        totalPlayTimeMinutes: user.totalPlayTimeMinutes + minutes,
        lastPlayedAt: DateTime.now(),
      );

      await updateUser(updatedUser);
    } catch (e, stackTrace) {
      _logger.e('Failed to add play time', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 获取用户统计信息
  Map<String, dynamic> getUserStats(String userId) {
    try {
      final user = getUserById(userId);
      if (user == null) return {};

      return {
        'totalStars': user.totalStars,
        'completedLevels': user.completedLevels,
        'totalPlayTimeMinutes': user.totalPlayTimeMinutes,
        'achievementsCount': user.unlockedAchievements.length,
        'skillLevels': {
          'pattern': user.getSkillLevel('pattern'),
          'spatial': user.getSkillLevel('spatial'),
          'numeric': user.getSkillLevel('numeric'),
          'coding': user.getSkillLevel('coding'),
        },
        'createdDaysAgo': DateTime.now().difference(user.createdAt).inDays,
        'lastPlayedDaysAgo': DateTime.now().difference(user.lastPlayedAt).inDays,
      };
    } catch (e, stackTrace) {
      _logger.e('Failed to get user stats', error: e, stackTrace: stackTrace);
      return {};
    }
  }

  /// 重置用户进度
  Future<void> resetUserProgress(String userId) async {
    try {
      final user = getUserById(userId);
      if (user == null) {
        throw UserNotFoundException(userId: userId);
      }

      final resetUser = user.copyWith(
        levelProgress: {},
        unlockedAchievements: [],
        skillPoints: {
          'pattern': 0,
          'spatial': 0,
          'numeric': 0,
          'coding': 0,
        },
      );

      await updateUser(resetUser);
      _logger.i('Reset progress for user $userId');
    } catch (e, stackTrace) {
      _logger.e('Failed to reset user progress', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }



  /// 关闭服务
  Future<void> dispose() async {
    try {
      await _userBox.close();
      await _settingsBox.close();
      _logger.i('UserService disposed');
    } catch (e, stackTrace) {
      _logger.e('Failed to dispose UserService', error: e, stackTrace: stackTrace);
    }
  }
}