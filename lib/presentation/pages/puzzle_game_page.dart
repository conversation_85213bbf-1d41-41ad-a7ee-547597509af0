import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/bloc_exports.dart';
import '../widgets/puzzle_grid_widget.dart';
import '../widgets/option_selector_widget.dart';
import '../widgets/spatial_visualization_widget.dart';
import '../widgets/numeric_logic_widget.dart';
import '../widgets/coding_widget.dart';
import '../widgets/mirror_symmetry_widget.dart';
import '../widgets/game_timer_widget.dart';
import '../../core/service_locator.dart';

import '../../domain/entities/puzzle_entity.dart';
import '../../domain/value_objects/puzzle_types.dart';
import '../../data/models/puzzle.dart';

class PuzzleGamePage extends StatelessWidget {
  final String levelId;
  final String userId;

  const PuzzleGamePage({
    super.key,
    required this.levelId,
    required this.userId,
  });

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) =>
              getIt<PuzzleBloc>()
                ..add(StartPuzzleEvent(levelId: levelId, userId: userId)),
        ),
        BlocProvider(create: (context) => getIt<HintBloc>()),
      ],
      child: PuzzleGameView(levelId: levelId, userId: userId),
    );
  }
}

class PuzzleGameView extends StatefulWidget {
  final String levelId;
  final String userId;

  const PuzzleGameView({
    super.key,
    required this.levelId,
    required this.userId,
  });

  @override
  State<PuzzleGameView> createState() => _PuzzleGameViewState();
}

class _PuzzleGameViewState extends State<PuzzleGameView>
    with TickerProviderStateMixin {
  late AnimationController _celebrationController;
  late AnimationController _shakeController;
  late Animation<double> _shakeAnimation;

  // 图形推理相关状态
  String? _selectedOption;

  // 空间想象相关状态
  String? _selectedSpatialOption;

  // 数字逻辑相关状态
  List<String?> _numericGrid = [];
  String? _selectedNumericItem;

  // 编程启蒙相关状态
  final List<String> _codingCommands = [];
  bool _isRunningCode = false;

  // 镜像对称相关状态
  String? _selectedMirrorOption;
  bool _isMirrorSubmitted = false;

  // 通用状态
  int _gameTimeSeconds = 0;
  bool _isGameActive = false;

  @override
  void initState() {
    super.initState();
    _celebrationController = AnimationController(
      duration: const Duration(milliseconds: 2500),
      vsync: this,
    );

    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _shakeAnimation = Tween<double>(begin: 0.0, end: 10.0).animate(
      CurvedAnimation(parent: _shakeController, curve: Curves.elasticIn),
    );
  }

  @override
  void dispose() {
    _celebrationController.dispose();
    _shakeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF6C5CE7), Color(0xFFA29BFE), Color(0xFFDDD6FE)],
          ),
        ),
        child: SafeArea(
          child: MultiBlocListener(
            listeners: [
              BlocListener<PuzzleBloc, PuzzleState>(
                listener: (context, state) {
                  if (state is PuzzleGameLoaded) {
                    setState(() {
                      _isGameActive = true;
                      _gameTimeSeconds = 0;
                    });
                    _startGameTimer();
                  } else if (state is PuzzleAnswerCorrect) {
                    _celebrationController.forward();
                    setState(() {
                      _isGameActive = false;
                    });
                    _showSuccessDialog(context);
                  } else if (state is PuzzleAnswerIncorrect) {
                    _shakeController.forward().then((_) {
                      _shakeController.reset();
                    });
                    _showIncorrectFeedback(context);
                  } else if (state is PuzzleError) {
                    _showErrorDialog(context, state.message);
                  }
                },
              ),
              BlocListener<HintBloc, HintState>(
                listener: (context, state) {
                  if (state is HintGenerated) {
                    _showHintDialog(context, {
                      'content': state.hintResult.content,
                      'type': state.hintResult.type,
                      'level': 1,
                    });
                  }
                },
              ),
            ],
            child: Column(
              children: [
                // 顶部游戏信息栏
                _buildGameHeader(),

                // 主要游戏区域
                Expanded(
                  child: BlocBuilder<PuzzleBloc, PuzzleState>(
                    builder: (context, state) {
                      if (state is PuzzleGameLoading) {
                        return _buildLoadingView();
                      } else if (state is PuzzleGameLoaded) {
                        return _buildGameContent(state.puzzle);
                      } else if (state is PuzzleError) {
                        return _buildErrorView(state.message);
                      }
                      return _buildLoadingView();
                    },
                  ),
                ),

                // 底部控制栏
                _buildBottomControls(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGameHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // 返回按钮
          GestureDetector(
            onTap: () => _showExitDialog(context),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.arrow_back_ios_new,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),

          const SizedBox(width: 16),

          // 游戏标题
          Expanded(
            child: BlocBuilder<PuzzleBloc, PuzzleState>(
              builder: (context, state) {
                if (state is PuzzleGameLoaded) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getPuzzleTypeTitle(state.puzzle.puzzleType),
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        _getPuzzleTypeSubtitle(state.puzzle.puzzleType),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  );
                }
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '加载中...',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                );
              },
            ),
          ),

          // 计时器
          GameTimerWidget(seconds: _gameTimeSeconds, isActive: _isGameActive),
        ],
      ),
    );
  }

  Widget _buildGameContent(PuzzleEntity puzzle) {
    return AnimatedBuilder(
      animation: _shakeAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(_shakeAnimation.value, 0),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // 题目描述
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Text(
                    puzzle.prompt,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: const Color(0xFF2D3436),
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                const SizedBox(height: 24),

                // 根据谜题类型显示不同的游戏组件
                Expanded(child: _buildPuzzleWidget(puzzle)),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildBottomControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // 提示按钮
          Expanded(
            child: BlocBuilder<HintBloc, HintState>(
              builder: (context, state) {
                final isGenerating = state is HintGenerating;
                final isOnCooldown = state is HintCooldown;

                return ElevatedButton.icon(
                  onPressed: (!isGenerating && !isOnCooldown)
                      ? _requestHint
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  icon: isGenerating
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                      : const Icon(Icons.lightbulb_outline),
                  label: Text(
                    isOnCooldown ? '冷却中...' : '提示',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                );
              },
            ),
          ),

          const SizedBox(width: 16),

          // 提交答案按钮
          Expanded(
            flex: 2,
            child: BlocBuilder<PuzzleBloc, PuzzleState>(
              builder: (context, state) {
                if (state is PuzzleGameLoaded) {
                  return ElevatedButton(
                    onPressed: _canSubmitAnswer(state.puzzle.puzzleType)
                        ? _submitSelectedAnswer
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: const Color(0xFF6C5CE7),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      _getSubmitButtonText(state.puzzle.puzzleType),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  );
                }
                return ElevatedButton(
                  onPressed: null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: const Color(0xFF6C5CE7),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    '提交答案',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingView() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
          SizedBox(height: 16),
          Text(
            '正在加载谜题...',
            style: TextStyle(color: Colors.white, fontSize: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.white.withValues(alpha: 0.7),
          ),
          const SizedBox(height: 16),
          Text(
            '加载失败',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: const Color(0xFF6C5CE7),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text('返回'),
          ),
        ],
      ),
    );
  }

  void _startGameTimer() {
    // 启动计时器（每秒更新）
    Future.doWhile(() async {
      if (!_isGameActive) return false;
      await Future.delayed(const Duration(seconds: 1));
      if (!mounted) return false;
      if (mounted && _isGameActive) {
        setState(() {
          _gameTimeSeconds++;
        });
        context.read<PuzzleBloc>().add(UpdateGameTimeEvent(_gameTimeSeconds));
      }
      return _isGameActive;
    });
  }

  void _requestHint() {
    final state = context.read<PuzzleBloc>().state;
    if (state is PuzzleGameLoaded) {
      context.read<HintBloc>().add(
        RequestHintEvent(
          levelId: state.puzzle.levelId,
          userId: widget.userId,
          currentHintLevel: 1,
        ),
      );
    }
  }

  void _showSuccessDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.celebration, size: 64, color: Colors.green),
            const SizedBox(height: 16),
            Text(
              '恭喜你！',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '答案正确！用时 ${_formatTime(_gameTimeSeconds)}',
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      Navigator.of(context).pop();
                    },
                    child: const Text('返回'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      // 触发加载下一关事件
                      context.read<PuzzleBloc>().add(
                        LoadNextPuzzleEvent(
                          currentLevelId: widget.levelId,
                          userId: widget.userId,
                        ),
                      );
                    },
                    child: const Text('下一关'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showIncorrectFeedback(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            Icon(Icons.close, color: Colors.white),
            SizedBox(width: 8),
            Text('答案不正确，再试试看！'),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  void _showHintDialog(BuildContext context, Map<String, dynamic> hint) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Icon(Icons.lightbulb, color: Colors.orange),
            const SizedBox(width: 8),
            const Text('提示'),
          ],
        ),
        content: Text(
          hint['content'] as String,
          style: Theme.of(context).textTheme.bodyLarge,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('知道了'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: const Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 8),
            Text('错误'),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showExitDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: const Text('确认退出'),
        content: const Text('确定要退出当前游戏吗？进度将不会保存。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('退出', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  // 获取谜题类型标题
  String _getPuzzleTypeTitle(PuzzleType puzzleType) {
    switch (puzzleType) {
      case PuzzleType.graphicPattern3x3:
        return '图形推理';
      case PuzzleType.spatialVisualization:
        return '空间想象';
      case PuzzleType.numericLogic:
        return '数字逻辑';
      case PuzzleType.introToCoding:
        return '编程启蒙';
      case PuzzleType.mirrorSymmetry:
        return '镜像对称';
    }
  }

  // 获取谜题类型副标题
  String _getPuzzleTypeSubtitle(PuzzleType puzzleType) {
    switch (puzzleType) {
      case PuzzleType.graphicPattern3x3:
        return '找出规律，选择正确答案';
      case PuzzleType.spatialVisualization:
        return '想象立体图形的形状';
      case PuzzleType.numericLogic:
        return '填充网格，避免重复';
      case PuzzleType.introToCoding:
        return '编写指令，帮助角色到达目标';
      case PuzzleType.mirrorSymmetry:
        return '理解镜像反射，选择正确图案';
    }
  }

  // 根据谜题类型构建不同的游戏组件
  Widget _buildPuzzleWidget(PuzzleEntity puzzle) {
    switch (puzzle.puzzleType) {
      case PuzzleType.graphicPattern3x3:
        return _buildGraphicPatternWidget(puzzle);
      case PuzzleType.spatialVisualization:
        return _buildSpatialVisualizationWidget(puzzle);
      case PuzzleType.numericLogic:
        return _buildNumericLogicWidget(puzzle);
      case PuzzleType.introToCoding:
        return _buildCodingWidget(puzzle);
      case PuzzleType.mirrorSymmetry:
        return _buildMirrorSymmetryWidget(puzzle);
    }
  }

  // 构建图形推理组件
  Widget _buildGraphicPatternWidget(PuzzleEntity puzzle) {
    final puzzleData = GraphicPatternData.fromJson(puzzle.data);

    return Column(
      children: [
        // 3x3 谜题网格
        Expanded(
          flex: 3,
          child: PuzzleGridWidget(
            puzzleData: puzzleData,
            selectedOption: _selectedOption,
            onGridTap: (index) {
              if (_selectedOption != null && index == puzzleData.emptyIndex) {
                _submitGraphicPatternAnswer(_selectedOption!);
              }
            },
          ),
        ),

        const SizedBox(height: 24),

        // 选项区域
        Expanded(
          flex: 2,
          child: OptionSelectorWidget(
            options: puzzleData.options,
            selectedOption: _selectedOption,
            onOptionSelected: (option) {
              setState(() {
                _selectedOption = option;
              });
            },
          ),
        ),
      ],
    );
  }

  // 构建空间想象组件
  Widget _buildSpatialVisualizationWidget(PuzzleEntity puzzle) {
    final puzzleData = SpatialVisualizationData.fromJson(puzzle.data);

    return SpatialVisualizationWidget(
      puzzleData: puzzleData,
      selectedOption: _selectedSpatialOption,
      onOptionSelected: (option) {
        setState(() {
          _selectedSpatialOption = option;
        });
      },
      showHint: false, // TODO: 根据提示状态设置
    );
  }

  // 构建数字逻辑组件
  Widget _buildNumericLogicWidget(PuzzleEntity puzzle) {
    final puzzleData = NumericLogicData.fromJson(puzzle.data);

    // 初始化网格状态
    if (_numericGrid.isEmpty) {
      _numericGrid = List.from(puzzleData.grid);
    }

    return NumericLogicWidget(
      puzzleData: puzzleData,
      currentGrid: _numericGrid,
      selectedItem: _selectedNumericItem,
      onGridTap: (index) {
        if (_selectedNumericItem != null && _numericGrid[index] == null) {
          setState(() {
            _numericGrid[index] = _selectedNumericItem;
          });
          _checkNumericLogicCompletion();
        }
      },
      onItemSelected: (item) {
        setState(() {
          _selectedNumericItem = item;
        });
      },
      showHint: false, // TODO: 根据提示状态设置
    );
  }

  // 构建编程启蒙组件
  Widget _buildCodingWidget(PuzzleEntity puzzle) {
    final puzzleData = CodingData.fromJson(puzzle.data);

    return CodingWidget(
      puzzleData: puzzleData,
      currentCommands: _codingCommands,
      onCommandAdded: (command) {
        if (_codingCommands.length < puzzleData.maxCommands) {
          setState(() {
            _codingCommands.add(command);
          });
        }
      },
      onCommandRemoved: (index) {
        setState(() {
          _codingCommands.removeAt(index);
        });
      },
      onCommandsCleared: () {
        setState(() {
          _codingCommands.clear();
        });
      },
      onRunCommands: () {
        _runCodingCommands();
      },
      isRunning: _isRunningCode,
      showHint: false, // TODO: 根据提示状态设置
    );
  }

  // 构建镜像对称组件
  Widget _buildMirrorSymmetryWidget(PuzzleEntity puzzle) {
    // 转换为数据模型需要的格式
    final puzzleModel = Puzzle.fromDomain(puzzle);

    return MirrorSymmetryWidget(
      puzzleData: puzzleModel.data,
      onAnswerSelected: (selectedAnswer) {
        setState(() {
          _selectedMirrorOption = selectedAnswer;
        });
        _submitMirrorSymmetryAnswer();
      },
      onHintRequested: () {
        // 请求提示
        context.read<HintBloc>().add(
          RequestHintEvent(
            levelId: puzzle.levelId,
            userId: widget.userId, // 从widget属性获取用户ID
            currentHintLevel: 1,
            currentGameState: {
              'selectedOption': _selectedMirrorOption,
              'gameType': 'mirror_symmetry',
            },
          ),
        );
      },
      onRestart: () {
        // 重新开始游戏
        setState(() {
          _selectedMirrorOption = null;
          _isMirrorSubmitted = false;
        });
        context.read<PuzzleBloc>().add(RestartPuzzleEvent());
      },
    );
  }

  // 提交图形推理答案
  void _submitGraphicPatternAnswer(String answer) {
    context.read<PuzzleBloc>().add(
      SubmitAnswerEvent(userAnswer: answer, timeSeconds: _gameTimeSeconds),
    );
  }

  // 提交空间想象答案
  void _submitSpatialAnswer() {
    if (_selectedSpatialOption != null) {
      context.read<PuzzleBloc>().add(
        SubmitAnswerEvent(
          userAnswer: _selectedSpatialOption!,
          timeSeconds: _gameTimeSeconds,
        ),
      );
    }
  }

  // 检查数字逻辑完成状态
  void _checkNumericLogicCompletion() {
    // 检查是否所有格子都已填充
    if (!_numericGrid.contains(null)) {
      // 提交答案
      context.read<PuzzleBloc>().add(
        SubmitAnswerEvent(
          userAnswer: _numericGrid,
          timeSeconds: _gameTimeSeconds,
        ),
      );
    }
  }

  // 运行编程指令
  void _runCodingCommands() {
    if (_codingCommands.isEmpty) return;

    setState(() {
      _isRunningCode = true;
    });

    // 模拟指令运行时间
    Future.delayed(const Duration(seconds: 2), () {
      if (!mounted) return;
      setState(() {
        _isRunningCode = false;
      });

      // 提交答案
      context.read<PuzzleBloc>().add(
        SubmitAnswerEvent(
          userAnswer: _codingCommands,
          timeSeconds: _gameTimeSeconds,
        ),
      );
    });
  }

  // 提交镜像对称答案
  void _submitMirrorSymmetryAnswer() {
    if (_selectedMirrorOption != null) {
      setState(() {
        _isMirrorSubmitted = true;
      });

      context.read<PuzzleBloc>().add(
        SubmitAnswerEvent(
          userAnswer: _selectedMirrorOption!,
          timeSeconds: _gameTimeSeconds,
        ),
      );
    }
  }

  // 更新提交答案方法以支持所有类型
  void _submitSelectedAnswer() {
    final state = context.read<PuzzleBloc>().state;
    if (state is PuzzleGameLoaded) {
      switch (state.puzzle.puzzleType) {
        case PuzzleType.graphicPattern3x3:
          if (_selectedOption != null) {
            _submitGraphicPatternAnswer(_selectedOption!);
          }
          break;
        case PuzzleType.spatialVisualization:
          _submitSpatialAnswer();
          break;
        case PuzzleType.numericLogic:
          _checkNumericLogicCompletion();
          break;
        case PuzzleType.introToCoding:
          _runCodingCommands();
          break;
        case PuzzleType.mirrorSymmetry:
          _submitMirrorSymmetryAnswer();
          break;
      }
    }
  }

  // 判断是否可以提交答案
  bool _canSubmitAnswer(PuzzleType puzzleType) {
    switch (puzzleType) {
      case PuzzleType.graphicPattern3x3:
        return _selectedOption != null;
      case PuzzleType.spatialVisualization:
        return _selectedSpatialOption != null;
      case PuzzleType.numericLogic:
        return !_numericGrid.contains(null);
      case PuzzleType.introToCoding:
        return _codingCommands.isNotEmpty && !_isRunningCode;
      case PuzzleType.mirrorSymmetry:
        return _selectedMirrorOption != null && !_isMirrorSubmitted;
    }
  }

  // 获取提交按钮文本
  String _getSubmitButtonText(PuzzleType puzzleType) {
    switch (puzzleType) {
      case PuzzleType.graphicPattern3x3:
      case PuzzleType.spatialVisualization:
      case PuzzleType.mirrorSymmetry:
        return '提交答案';
      case PuzzleType.numericLogic:
        return '检查答案';
      case PuzzleType.introToCoding:
        return _isRunningCode ? '运行中...' : '运行指令';
    }
  }
}
