import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/bloc_exports.dart';
import '../../core/service_locator.dart';
import '../../domain/entities/user_profile_entity.dart';

class UserSelectionPage extends StatelessWidget {
  const UserSelectionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<UserBloc>()..add(LoadUsersEvent()),
      child: const UserSelectionView(),
    );
  }
}

class UserSelectionView extends StatelessWidget {
  const UserSelectionView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF6C5CE7),
              Color(0xFFA29BFE),
            ],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              children: [
                const SizedBox(height: 40),
                // 标题区域
                Text(
                  '选择你的角色',
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '选择一个角色开始你的逻辑冒险之旅',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.white.withOpacity(0.8),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 40),
                
                // 用户列表区域
                Expanded(
                  child: BlocConsumer<UserBloc, UserState>(
                    listener: (context, state) {
                      if (state is UserSelected) {
                        // 用户选择成功，导航到主页
                        Navigator.of(context).pushReplacementNamed('/home');
                      } else if (state is UserError) {
                        // 显示错误信息
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(state.message),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    },
                    builder: (context, state) {
                      if (state is UserLoading) {
                        return const Center(
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        );
                      } else if (state is UsersLoaded) {
                        return _buildUserGrid(context, state.users);
                      } else if (state is UserError) {
                        return _buildErrorView(context, state.message);
                      }
                      return const Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserGrid(BuildContext context, List<UserProfileEntity> users) {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 0.8,
      ),
      itemCount: users.length + (users.length < 4 ? 1 : 0), // 最多4个用户
      itemBuilder: (context, index) {
        if (index < users.length) {
          return _buildUserCard(context, users[index]);
        } else {
          return _buildAddUserCard(context);
        }
      },
    );
  }

  Widget _buildUserCard(BuildContext context, UserProfileEntity user) {
    return GestureDetector(
      onTap: () {
        context.read<UserBloc>().add(SelectUserEvent(user.id));
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 头像
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _getAvatarColor(user.avatarId),
                boxShadow: [
                  BoxShadow(
                    color: _getAvatarColor(user.avatarId).withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(
                _getAvatarIcon(user.avatarId),
                size: 40,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            
            // 昵称
            Text(
              user.nickname,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: const Color(0xFF2D3436),
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            
            // 等级和技能点
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFF6C5CE7).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'Lv.${user.currentLevel} • ${user.totalPoints}分',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: const Color(0xFF6C5CE7),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(height: 12),
            
            // 进度指示器
            LinearProgressIndicator(
              value: _calculateLevelProgress(user),
              backgroundColor: Colors.grey.withOpacity(0.2),
              valueColor: AlwaysStoppedAnimation<Color>(_getAvatarColor(user.avatarId)),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddUserCard(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pushNamed('/user_creation');
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withOpacity(0.5),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.grey.withOpacity(0.2),
                border: Border.all(
                  color: Colors.grey.withOpacity(0.3),
                  width: 2,
                  style: BorderStyle.solid,
                ),
              ),
              child: const Icon(
                Icons.add,
                size: 40,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '添加新用户',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              '创建一个新的游戏角色',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorView(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.white.withOpacity(0.7),
          ),
          const SizedBox(height: 16),
          Text(
            '出错了',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              context.read<UserBloc>().add(LoadUsersEvent());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: const Color(0xFF6C5CE7),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  Color _getAvatarColor(String avatarId) {
    switch (avatarId) {
      case 'avatar_1':
        return const Color(0xFF6C5CE7);
      case 'avatar_2':
        return const Color(0xFFA29BFE);
      case 'avatar_3':
        return const Color(0xFF74B9FF);
      case 'avatar_4':
        return const Color(0xFF00B894);
      case 'avatar_5':
        return const Color(0xFFE17055);
      case 'avatar_6':
        return const Color(0xFFE84393);
      case 'avatar_7':
        return const Color(0xFFFD79A8);
      case 'avatar_8':
        return const Color(0xFFFDCB6E);
      case 'avatar_9':
        return const Color(0xFF6C5CE7);
      case 'avatar_10':
        return const Color(0xFF00CEC9);
      case 'avatar_11':
        return const Color(0xFFB2BEC3);
      case 'avatar_12':
        return const Color(0xFF636E72);
      default:
        return const Color(0xFF6C5CE7);
    }
  }

  IconData _getAvatarIcon(String avatarId) {
    switch (avatarId) {
      case 'avatar_1':
        return Icons.child_care;
      case 'avatar_2':
        return Icons.face;
      case 'avatar_3':
        return Icons.pets;
      case 'avatar_4':
        return Icons.sports_esports;
      case 'avatar_5':
        return Icons.school;
      case 'avatar_6':
        return Icons.star;
      case 'avatar_7':
        return Icons.favorite;
      case 'avatar_8':
        return Icons.wb_sunny;
      case 'avatar_9':
        return Icons.rocket_launch;
      case 'avatar_10':
        return Icons.palette;
      case 'avatar_11':
        return Icons.music_note;
      case 'avatar_12':
        return Icons.sports_soccer;
      default:
        return Icons.person;
    }
  }

  double _calculateLevelProgress(UserProfileEntity user) {
    // 简单的等级进度计算逻辑
    // 假设每级需要100分
    int pointsForCurrentLevel = (user.currentLevel - 1) * 100;
    int pointsForNextLevel = user.currentLevel * 100;
    int currentLevelPoints = user.totalPoints - pointsForCurrentLevel;
    
    if (currentLevelPoints <= 0) return 0.0;
    if (user.totalPoints >= pointsForNextLevel) return 1.0;
    
    return currentLevelPoints / 100.0;
  }
} 