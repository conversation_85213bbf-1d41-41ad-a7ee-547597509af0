import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/bloc_exports.dart';
import '../widgets/mirror_symmetry_widget.dart';
import '../../core/service_locator.dart';
import '../../data/models/puzzle.dart';
import '../../services/puzzle_engine.dart';

/// 镜像对称游戏测试页面
///
/// 专门用于测试镜像对称游戏功能的独立页面
/// 可以直接加载和测试镜像对称谜题
class MirrorSymmetryTestPage extends StatelessWidget {
  const MirrorSymmetryTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => getIt<PuzzleBloc>()),
        BlocProvider(create: (context) => getIt<HintBloc>()),
      ],
      child: const MirrorSymmetryTestView(),
    );
  }
}

class MirrorSymmetryTestView extends StatefulWidget {
  const MirrorSymmetryTestView({super.key});

  @override
  State<MirrorSymmetryTestView> createState() => _MirrorSymmetryTestViewState();
}

class _MirrorSymmetryTestViewState extends State<MirrorSymmetryTestView> {
  String? _selectedOption;
  bool _isSubmitted = false;
  bool? _isCorrect;
  Puzzle? _currentPuzzle;

  // 可用的测试谜题
  final List<String> _testPuzzles = [
    'mirror_symmetry_sample',
    'mirror_symmetry_vertical',
    'mirror_symmetry_advanced',
    'mirror_symmetry_with_explanation',
  ];

  int _currentPuzzleIndex = 0;

  @override
  void initState() {
    super.initState();
    _loadTestPuzzle();
  }

  Future<void> _loadTestPuzzle() async {
    try {
      final puzzleEngine = getIt<PuzzleEngine>();
      final puzzle = await puzzleEngine.loadPuzzle(
        _testPuzzles[_currentPuzzleIndex],
      );

      setState(() {
        _currentPuzzle = puzzle;
        _selectedOption = null;
        _isSubmitted = false;
        _isCorrect = null;
      });

      // print('✅ 加载谜题成功: \${_testPuzzles[_currentPuzzleIndex]}');
    } catch (e) {
      // print('❌ 加载谜题失败: $e');
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('加载谜题失败: $e')));
    }
  }



  void _onSubmit() {
    if (_selectedOption == null || _currentPuzzle == null) return;

    setState(() {
      _isSubmitted = true;
    });

    // 验证答案
    try {
      final puzzleData = MirrorSymmetryData.fromJson(_currentPuzzle!.data);
      final isCorrect = puzzleData.isCorrectAnswer(_selectedOption!);

      setState(() {
        _isCorrect = isCorrect;
      });

      // 显示结果反馈
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isCorrect ? '🎉 答案正确！' : '❌ 答案错误，正确答案是：${puzzleData.answer}',
          ),
          backgroundColor: isCorrect ? Colors.green : Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );

      // print('答案验证结果: ${isCorrect ? "正确" : "错误"}');
      // print('用户答案: $_selectedOption');
      // print('正确答案: ${puzzleData.answer}');
    } catch (e) {
      // print('❌ 答案验证失败: $e');
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('答案验证失败: $e')));
    }
  }

  void _nextPuzzle() {
    setState(() {
      _currentPuzzleIndex = (_currentPuzzleIndex + 1) % _testPuzzles.length;
    });
    _loadTestPuzzle();
  }

  void _previousPuzzle() {
    setState(() {
      _currentPuzzleIndex =
          (_currentPuzzleIndex - 1 + _testPuzzles.length) % _testPuzzles.length;
    });
    _loadTestPuzzle();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('镜像对称游戏测试'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadTestPuzzle,
            tooltip: '重新加载',
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF00CEC9), Color(0xFF00B894), Color(0xFF81ECEC)],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 测试信息栏
              _buildTestInfoBar(),

              // 游戏内容区域
              Expanded(
                child: _currentPuzzle != null
                    ? _buildGameContent()
                    : _buildLoadingView(),
              ),

              // 控制按钮
              _buildControlButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTestInfoBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '测试谜题 ${_currentPuzzleIndex + 1}/${_testPuzzles.length}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.teal,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '当前谜题: ${_testPuzzles[_currentPuzzleIndex]}',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
          ),
          if (_isSubmitted) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  _isCorrect == true ? Icons.check_circle : Icons.cancel,
                  color: _isCorrect == true ? Colors.green : Colors.red,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  _isCorrect == true ? '答案正确' : '答案错误',
                  style: TextStyle(
                    color: _isCorrect == true ? Colors.green : Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildGameContent() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: MirrorSymmetryWidget(
        puzzleData: _currentPuzzle!.data,
        onAnswerSelected: (selectedAnswer) {
          setState(() {
            _selectedOption = selectedAnswer;
          });
          // 自动提交答案进行验证
          _onSubmit();
        },
        onHintRequested: () {
          // TODO: 实现提示请求逻辑
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('提示功能开发中...')));
        },
        onRestart: () {
          _loadTestPuzzle();
        },
      ),
    );
  }

  Widget _buildLoadingView() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: Colors.white),
          SizedBox(height: 16),
          Text(
            '正在加载谜题...',
            style: TextStyle(color: Colors.white, fontSize: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildControlButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // 上一题按钮
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _previousPuzzle,
              icon: const Icon(Icons.arrow_back),
              label: const Text('上一题'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),

          const SizedBox(width: 16),

          // 重置按钮
          ElevatedButton(
            onPressed: () {
              setState(() {
                _selectedOption = null;
                _isSubmitted = false;
                _isCorrect = null;
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Icon(Icons.refresh),
          ),

          const SizedBox(width: 16),

          // 下一题按钮
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _nextPuzzle,
              icon: const Icon(Icons.arrow_forward),
              label: const Text('下一题'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
