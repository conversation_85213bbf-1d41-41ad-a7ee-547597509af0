import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../core/service_locator.dart';
import '../../domain/repositories/user_repository.dart';
import '../../domain/entities/user_profile_entity.dart';
import '../bloc/bloc_exports.dart';
import '../widgets/settings/volume_slider_widget.dart';
import '../widgets/settings/language_selector_widget.dart';
import '../widgets/settings/time_limit_selector_widget.dart';
import '../widgets/settings/time_slots_selector_widget.dart';
import '../widgets/settings/theme_settings_widget.dart';
import '../../core/theme/ux_theme_config.dart';

/// 设置页面
class SettingsPage extends StatefulWidget {
  final String? userId;

  const SettingsPage({
    super.key,
    this.userId,
  });

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  late String _currentUserId;

  @override
  void initState() {
    super.initState();
    _initializeUserId();
  }

  Future<void> _initializeUserId() async {
    if (widget.userId != null) {
      _currentUserId = widget.userId!;
    } else {
      // 从UserRepository获取当前用户ID
      final userRepository = sl<UserRepository>();
      final currentUserResult = await userRepository.getCurrentUser();
      if (currentUserResult.isSuccess && currentUserResult.data != null) {
        _currentUserId = currentUserResult.data!.id;
      } else {
        // 如果没有当前用户，返回上级页面
        if (mounted) {
          Navigator.of(context).pop();
        }
        return;
      }
    }

    // 加载设置
    if (mounted) {
      context.read<SettingsBloc>().add(LoadSettingsEvent(_currentUserId));
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<SettingsBloc>(),
      child: Scaffold(
        backgroundColor: UXThemeConfig.backgroundPrimary,
        appBar: _buildAppBar(context),
        body: BlocConsumer<SettingsBloc, SettingsState>(
          listener: _handleStateChanges,
          builder: (context, state) {
            return _buildBody(context, state);
          },
        ),
      ),
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: const Text('设置'),
      backgroundColor: UXThemeConfig.primaryBlue,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        BlocBuilder<SettingsBloc, SettingsState>(
          builder: (context, state) {
            final hasChanges = state is SettingsLoaded && state.hasUnsavedChanges;
            
            return AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: hasChanges
                  ? TextButton.icon(
                      key: const ValueKey('save_button'),
                      onPressed: () => _saveSettings(context, state),
                      icon: const Icon(Icons.save, color: Colors.white),
                      label: const Text(
                        '保存',
                        style: TextStyle(color: Colors.white),
                      ),
                    )
                  : const SizedBox.shrink(key: ValueKey('empty')),
            );
          },
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: Colors.white),
          onSelected: (value) => _handleMenuAction(context, value),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'reset',
              child: Row(
                children: [
                  Icon(Icons.restore, color: UXThemeConfig.warningOrange),
                  SizedBox(width: 8),
                  Text('重置设置'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'help',
              child: Row(
                children: [
                  Icon(Icons.help_outline, color: UXThemeConfig.primaryBlue),
                  SizedBox(width: 8),
                  Text('帮助'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建页面主体
  Widget _buildBody(BuildContext context, SettingsState state) {
    if (state is SettingsLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (state is SettingsError) {
      return _buildErrorView(context, state);
    }

    if (state is SettingsLoaded ||
        state is VolumeAdjusting ||
        state is LanguageChanging ||
        state is TimeLimitSetting ||
        state is SettingsSaving ||
        state is SettingsSaved) {
      return _buildSettingsView(context, state);
    }

    if (state is SettingsResetting) {
      return _buildResetView(context);
    }

    return const Center(
      child: Text('正在初始化设置...'),
    );
  }

  /// 构建错误视图
  Widget _buildErrorView(BuildContext context, SettingsError state) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: UXThemeConfig.errorRed,
            ),
            const SizedBox(height: 16),
            Text(
              '设置加载失败',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: UXThemeConfig.errorRed,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              state.userFriendlyMessage,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                context.read<SettingsBloc>().add(ClearSettingsErrorEvent());
                context.read<SettingsBloc>().add(LoadSettingsEvent(_currentUserId));
              },
              icon: const Icon(Icons.refresh),
              label: const Text('重试'),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建重置视图
  Widget _buildResetView(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('正在重置设置...'),
        ],
      ),
    );
  }

  /// 构建设置视图
  Widget _buildSettingsView(BuildContext context, SettingsState state) {
    final settings = _getSettingsFromState(state);
    if (settings == null) return const SizedBox.shrink();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 状态指示器
          _buildStatusIndicator(context, state),
          
          // 主题设置
          const ThemeSettingsWidget(),
          
          const SizedBox(height: 24),
          
          // 音频设置
          _buildAudioSection(context, settings, state),
          
          const SizedBox(height: 24),
          
          // 游戏设置
          _buildGameSection(context, settings, state),
          
          const SizedBox(height: 24),
          
          // 家长控制
          _buildParentalControlSection(context, settings, state),
          
          const SizedBox(height: 24),
          
          // 其他设置
          _buildOtherSection(context, settings, state),
          
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  /// 构建状态指示器
  Widget _buildStatusIndicator(BuildContext context, SettingsState state) {
    if (state is SettingsSaving) {
      return Container(
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: UXThemeConfig.primaryBlue.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: UXThemeConfig.primaryBlue.withValues(alpha: 0.3)),
        ),
        child: const Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            SizedBox(width: 12),
            Text('正在保存设置...'),
          ],
        ),
      );
    }

    if (state is SettingsSaved) {
      return Container(
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: UXThemeConfig.successGreen.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: UXThemeConfig.successGreen.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Icon(
              Icons.check_circle,
              color: UXThemeConfig.successGreen,
              size: 16,
            ),
            const SizedBox(width: 12),
            Text(state.message),
          ],
        ),
      );
    }

    if (state is LanguageChanging) {
      return Container(
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: UXThemeConfig.primaryBlue.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            SizedBox(width: 12),
            Text('正在切换语言...'),
          ],
        ),
      );
    }

    return const SizedBox.shrink();
  }

  /// 构建音频设置区域
  Widget _buildAudioSection(
    BuildContext context,
    UserSettingsEntity settings,
    SettingsState state,
  ) {
    return _buildSection(
      title: '音频设置',
      icon: Icons.volume_up,
      children: [
        VolumeSliderWidget(
          title: '背景音乐',
          icon: Icons.music_note,
          value: settings.musicVolume,
          isAdjusting: state is VolumeAdjusting && state.volumeType == 'music',
          onChanged: (value) {
            context.read<SettingsBloc>().add(UpdateMusicVolumeEvent(value));
          },
        ),
        const SizedBox(height: 16),
        VolumeSliderWidget(
          title: '游戏音效',
          icon: Icons.audiotrack,
          value: settings.sfxVolume,
          isAdjusting: state is VolumeAdjusting && state.volumeType == 'sfx',
          onChanged: (value) {
            context.read<SettingsBloc>().add(UpdateSfxVolumeEvent(value));
          },
        ),
      ],
    );
  }

  /// 构建游戏设置区域
  Widget _buildGameSection(
    BuildContext context,
    UserSettingsEntity settings,
    SettingsState state,
  ) {
    return _buildSection(
      title: '游戏设置',
      icon: Icons.games,
      children: [
        _buildSwitchTile(
          title: '震动反馈',
          subtitle: '答题错误时震动提醒',
          icon: Icons.vibration,
          value: settings.enableVibration,
          onChanged: (value) {
            context.read<SettingsBloc>().add(ToggleVibrationEvent(value));
          },
        ),
        _buildSwitchTile(
          title: '动画效果',
          subtitle: '启用游戏中的动画效果',
          icon: Icons.animation,
          value: settings.enableAnimations,
          onChanged: (value) {
            context.read<SettingsBloc>().add(ToggleAnimationsEvent(value));
          },
        ),
        const SizedBox(height: 16),
        LanguageSelectorWidget(
          currentLanguage: settings.language,
          isChanging: state is LanguageChanging,
          onLanguageChanged: (language) {
            context.read<SettingsBloc>().add(ChangeLanguageEvent(language));
          },
        ),
      ],
    );
  }

  /// 构建家长控制区域
  Widget _buildParentalControlSection(
    BuildContext context,
    UserSettingsEntity settings,
    SettingsState state,
  ) {
    return _buildSection(
      title: '家长控制',
      icon: Icons.family_restroom,
      children: [
        TimeLimitSelectorWidget(
          currentMinutes: settings.dailyTimeLimitMinutes,
          isSetting: state is TimeLimitSetting,
          onTimeLimitChanged: (minutes) {
            context.read<SettingsBloc>().add(UpdateDailyTimeLimitEvent(minutes));
          },
        ),
        const SizedBox(height: 16),
        TimeSlotselectorWidget(
          disabledTimeSlots: settings.disabledTimeSlots,
          onTimeSlotsChanged: (timeSlots) {
            context.read<SettingsBloc>().add(UpdateDisabledTimeSlotsEvent(timeSlots));
          },
        ),
      ],
    );
  }

  /// 构建其他设置区域
  Widget _buildOtherSection(
    BuildContext context,
    UserSettingsEntity settings,
    SettingsState state,
  ) {
    return _buildSection(
      title: '其他',
      icon: Icons.settings,
      children: [
        _buildActionTile(
          title: '清除缓存',
          subtitle: '清除游戏缓存数据',
          icon: Icons.cleaning_services,
          onTap: () => _showClearCacheDialog(context),
        ),
        _buildActionTile(
          title: '数据导出',
          subtitle: '导出游戏进度数据',
          icon: Icons.file_download,
          onTap: () => _showExportDataDialog(context),
        ),
        _buildActionTile(
          title: '关于应用',
          subtitle: '版本信息和开发者信息',
          icon: Icons.info,
          onTap: () => _showAboutDialog(context),
        ),
      ],
    );
  }

  /// 构建设置区域
  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: UXThemeConfig.primaryBlue),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: UXThemeConfig.primaryBlue,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  /// 构建开关磁贴
  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: Icon(icon, color: UXThemeConfig.primaryBlue),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: UXThemeConfig.primaryBlue,
      ),
    );
  }

  /// 构建动作磁贴
  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: Icon(icon, color: UXThemeConfig.primaryBlue),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  /// 处理状态变化
  void _handleStateChanges(BuildContext context, SettingsState state) {
    if (state is SettingsError) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(state.userFriendlyMessage),
          backgroundColor: UXThemeConfig.errorRed,
          action: SnackBarAction(
            label: '重试',
            textColor: Colors.white,
            onPressed: () {
              context.read<SettingsBloc>().add(ClearSettingsErrorEvent());
            },
          ),
        ),
      );
    }

    if (state is LanguageChanged) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('语言已切换为${_getLanguageDisplayName(state.language)}'),
          backgroundColor: UXThemeConfig.successGreen,
        ),
      );
    }
  }

  /// 从状态获取设置
  UserSettingsEntity? _getSettingsFromState(SettingsState state) {
    if (state is SettingsLoaded) return state.settings;
    if (state is VolumeAdjusting) return state.settings;
    if (state is LanguageChanging) return state.settings;
    if (state is TimeLimitSetting) return state.settings;
    if (state is SettingsSaving) return state.settings;
    if (state is SettingsSaved) return state.settings;
    return null;
  }

  /// 保存设置
  void _saveSettings(BuildContext context, SettingsState state) {
    final settings = _getSettingsFromState(state);
    if (settings != null) {
      context.read<SettingsBloc>().add(
            SaveSettingsEvent(
              userId: _currentUserId,
              settings: settings,
            ),
          );
    }
  }

  /// 处理菜单动作
  void _handleMenuAction(BuildContext context, String action) {
    switch (action) {
      case 'reset':
        _showResetDialog(context);
        break;
      case 'help':
        _showHelpDialog(context);
        break;
    }
  }

  /// 显示重置对话框
  void _showResetDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('重置设置'),
        content: const Text('确定要将所有设置重置为默认值吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<SettingsBloc>().add(const ResetSettingsEvent());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: UXThemeConfig.warningOrange,
            ),
            child: const Text('重置'),
          ),
        ],
      ),
    );
  }

  /// 显示帮助对话框
  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('设置帮助'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('音频设置：', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• 背景音乐：调节游戏背景音乐音量'),
              Text('• 游戏音效：调节游戏音效音量'),
              SizedBox(height: 16),
              Text('游戏设置：', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• 震动反馈：答题错误时设备震动提醒'),
              Text('• 动画效果：启用或禁用游戏动画'),
              Text('• 语言：切换应用界面语言'),
              SizedBox(height: 16),
              Text('家长控制：', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• 每日时间限制：限制每天游戏时间'),
              Text('• 禁用时间段：设置不允许游戏的时间段'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('知道了'),
          ),
        ],
      ),
    );
  }

  /// 显示清除缓存对话框
  void _showClearCacheDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清除缓存'),
        content: const Text('确定要清除游戏缓存吗？这将删除临时文件，但不会影响游戏进度。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: 实现清除缓存功能
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('缓存已清除')),
              );
            },
            child: const Text('清除'),
          ),
        ],
      ),
    );
  }

  /// 显示数据导出对话框
  void _showExportDataDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('数据导出'),
        content: const Text('将游戏进度数据导出为文件，可用于备份或迁移到其他设备。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: 实现数据导出功能
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('数据导出功能开发中')),
              );
            },
            child: const Text('导出'),
          ),
        ],
      ),
    );
  }

  /// 显示关于对话框
  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'LogicLab',
      applicationVersion: '1.0.0',
      applicationIcon: Container(
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          color: UXThemeConfig.primaryBlue,
          borderRadius: BorderRadius.circular(16),
        ),
        child: const Icon(
          Icons.psychology,
          color: Colors.white,
          size: 32,
        ),
      ),
      children: const [
        Text('LogicLab是一款专为6-12岁儿童设计的逻辑思维训练应用。'),
        SizedBox(height: 8),
        Text('通过有趣的谜题和游戏，帮助孩子提升逻辑思维能力。'),
      ],
    );
  }

  /// 获取语言显示名称
  String _getLanguageDisplayName(String languageCode) {
    switch (languageCode) {
      case 'zh_CN':
        return '简体中文';
      case 'zh_TW':
        return '繁體中文';
      case 'en_US':
        return 'English';
      default:
        return '简体中文';
    }
  }
} 