import 'package:flutter/material.dart';
import '../../core/theme/ux_theme_config.dart';

/// 主题演示页面
/// 
/// 展示应用的主题系统，包括颜色、字体、组件样式等
/// 支持实时切换主题模式和主题世界
class ThemeDemoPage extends StatefulWidget {
  const ThemeDemoPage({super.key});

  @override
  State<ThemeDemoPage> createState() => _ThemeDemoPageState();
}

class _ThemeDemoPageState extends State<ThemeDemoPage> {
  bool _isDarkMode = false;
  String _currentWorld = 'default';

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: _getSelectedTheme(),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('主题演示'),
          actions: [
            const Text('深色模式'),
            Switch(
              value: _isDarkMode,
              onChanged: (value) => setState(() => _isDarkMode = value),
            ),
            SizedBox(width: UXThemeConfig.paddingM),
          ],
        ),
        body: ListView(
          padding: EdgeInsets.all(UXThemeConfig.paddingM),
          children: [
            _buildThemeWorldSelector(),
            SizedBox(height: UXThemeConfig.paddingXL),
            _buildColorSection(),
            SizedBox(height: UXThemeConfig.paddingXL),
            _buildTypographySection(),
            SizedBox(height: UXThemeConfig.paddingXL),
            _buildComponentSection(),
            SizedBox(height: UXThemeConfig.paddingXL),
            _buildGameElementsSection(),
            SizedBox(height: UXThemeConfig.paddingXL),
            _buildResponsiveSection(),
          ],
        ),
      ),
    );
  }

  /// 获取选中的主题
  ThemeData _getSelectedTheme() {
    // 简化版本，直接使用MaterialApp的默认主题
    return _isDarkMode ? ThemeData.dark() : ThemeData.light();
  }

  /// 主题世界选择器
  Widget _buildThemeWorldSelector() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(UXThemeConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '主题世界',
              style: TextStyle(
                fontSize: UXThemeConfig.fontSizeL,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: UXThemeConfig.paddingM),
            Wrap(
              spacing: UXThemeConfig.paddingS,
              runSpacing: UXThemeConfig.paddingS,
              children: [
                _buildWorldChip('default', '默认', Icons.home),
                _buildWorldChip('forest', '森林', Icons.forest),
                _buildWorldChip('ocean', '海洋', Icons.waves),
                _buildWorldChip('space', '太空', Icons.rocket_launch),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 主题世界选择芯片
  Widget _buildWorldChip(String worldId, String label, IconData icon) {
    final isSelected = _currentWorld == worldId;
    return FilterChip(
      selected: isSelected,
      onSelected: (selected) => setState(() => _currentWorld = worldId),
      avatar: Icon(icon, size: 18),
      label: Text(label),
      selectedColor: UXThemeConfig.primaryBlue.withOpacity(0.2),
      checkmarkColor: UXThemeConfig.primaryBlue,
    );
  }

  /// 颜色展示区域
  Widget _buildColorSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(UXThemeConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '颜色系统',
              style: TextStyle(
                fontSize: UXThemeConfig.fontSizeL,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: UXThemeConfig.paddingM),
            
            // 主色调
            _buildColorRow('主色调', [
              ('主蓝色', UXThemeConfig.primaryBlue),
              ('强调蓝', UXThemeConfig.accentBlue),
              ('强调青', UXThemeConfig.accentTeal),
              ('次要橙', UXThemeConfig.secondaryOrange),
            ]),
            
            SizedBox(height: UXThemeConfig.paddingM),
            
            // 功能色
            _buildColorRow('功能色', [
              ('成功', UXThemeConfig.successGreen),
              ('警告', UXThemeConfig.warningOrange),
              ('错误', UXThemeConfig.errorRed),
              ('信息', UXThemeConfig.infoBlue),
            ]),
            
            SizedBox(height: UXThemeConfig.paddingM),
            
            // 文本色
            _buildColorRow('文本色', [
              ('深色文本', UXThemeConfig.textDark),
              ('中等文本', UXThemeConfig.textSecondary),
              ('浅色文本', Colors.white),
              ('次要文本', UXThemeConfig.textSecondary),
            ]),
          ],
        ),
      ),
    );
  }

  /// 构建颜色行
  Widget _buildColorRow(String title, List<(String, Color)> colors) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: UXThemeConfig.fontSizeM,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: UXThemeConfig.paddingS),
        Wrap(
          spacing: UXThemeConfig.paddingS,
          runSpacing: UXThemeConfig.paddingS,
          children: colors.map((colorInfo) {
            return _buildColorTile(colorInfo.$1, colorInfo.$2);
          }).toList(),
        ),
      ],
    );
  }

  /// 颜色瓦片
  Widget _buildColorTile(String name, Color color) {
    return Container(
      width: 80,
      height: 60,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusS),
        boxShadow: [
          BoxShadow(
            color: UXThemeConfig.shadowLight,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Text(
          name,
          style: TextStyle(
            color: _getContrastColor(color),
            fontSize: UXThemeConfig.fontSizeXS,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  /// 获取对比色
  Color _getContrastColor(Color color) {
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  /// 字体展示区域
  Widget _buildTypographySection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(UXThemeConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '字体系统',
              style: TextStyle(
                fontSize: UXThemeConfig.fontSizeL,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: UXThemeConfig.paddingM),
            
            _buildTypographyItem('超大标题', UXThemeConfig.fontSizeXL, FontWeight.bold),
            _buildTypographyItem('大标题', UXThemeConfig.fontSizeL, FontWeight.bold),
            _buildTypographyItem('中等标题', UXThemeConfig.fontSizeM, FontWeight.w600),
            _buildTypographyItem('正文', UXThemeConfig.fontSizeBody, FontWeight.normal),
            _buildTypographyItem('小文本', UXThemeConfig.fontSizeS, FontWeight.normal),
            _buildTypographyItem('超小文本', UXThemeConfig.fontSizeXS, FontWeight.normal),
          ],
        ),
      ),
    );
  }

  /// 字体样式项
  Widget _buildTypographyItem(String label, double fontSize, FontWeight fontWeight) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: UXThemeConfig.paddingS),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontSize: UXThemeConfig.fontSizeS,
                color: UXThemeConfig.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              '这是$label的示例文本',
              style: TextStyle(
                fontSize: fontSize,
                fontWeight: fontWeight,
                color: UXThemeConfig.textDark,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 组件展示区域
  Widget _buildComponentSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(UXThemeConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '组件样式',
              style: TextStyle(
                fontSize: UXThemeConfig.fontSizeL,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: UXThemeConfig.paddingM),
            
            // 按钮样式
            _buildButtonRow(),
            SizedBox(height: UXThemeConfig.paddingM),
            
            // 输入框样式
            _buildInputRow(),
            SizedBox(height: UXThemeConfig.paddingM),
            
            // 卡片样式
            _buildCardRow(),
          ],
        ),
      ),
    );
  }

  /// 按钮行
  Widget _buildButtonRow() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '按钮',
          style: TextStyle(
            fontSize: UXThemeConfig.fontSizeM,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: UXThemeConfig.paddingS),
        Wrap(
          spacing: UXThemeConfig.paddingS,
          runSpacing: UXThemeConfig.paddingS,
          children: [
            ElevatedButton(
              onPressed: () {},
              style: ElevatedButton.styleFrom(
                backgroundColor: UXThemeConfig.primaryBlue,
                foregroundColor: Colors.white,
              ),
              child: const Text('主要按钮'),
            ),
            ElevatedButton(
              onPressed: () {},
              style: ElevatedButton.styleFrom(
                backgroundColor: UXThemeConfig.accentBlue,
                foregroundColor: Colors.white,
              ),
              child: const Text('强调按钮'),
            ),
            OutlinedButton(
              onPressed: () {},
              child: const Text('轮廓按钮'),
            ),
            TextButton(
              onPressed: () {},
              child: const Text('文本按钮'),
            ),
          ],
        ),
      ],
    );
  }

  /// 输入框行
  Widget _buildInputRow() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '输入框',
          style: TextStyle(
            fontSize: UXThemeConfig.fontSizeM,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: UXThemeConfig.paddingS),
        Row(
          children: [
            Expanded(
              child: TextField(
                decoration: InputDecoration(
                  labelText: '标准输入框',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
                  ),
                ),
              ),
            ),
            SizedBox(width: UXThemeConfig.paddingM),
            Expanded(
              child: TextField(
                decoration: InputDecoration(
                  labelText: '带图标',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 卡片行
  Widget _buildCardRow() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '卡片',
          style: TextStyle(
            fontSize: UXThemeConfig.fontSizeM,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: UXThemeConfig.paddingS),
        Row(
          children: [
            Expanded(
              child: Card(
                child: Padding(
                  padding: EdgeInsets.all(UXThemeConfig.paddingM),
                  child: Column(
                    children: [
                      Icon(
                        Icons.star,
                        color: UXThemeConfig.accentTeal,
                        size: 32,
                      ),
                      SizedBox(height: UXThemeConfig.paddingS),
                      const Text('标准卡片'),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(width: UXThemeConfig.paddingM),
            Expanded(
              child: Card(
                elevation: 8,
                child: Padding(
                  padding: EdgeInsets.all(UXThemeConfig.paddingM),
                  child: Column(
                    children: [
                      Icon(
                        Icons.favorite,
                        color: UXThemeConfig.errorRed,
                        size: 32,
                      ),
                      SizedBox(height: UXThemeConfig.paddingS),
                      const Text('高阴影卡片'),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 游戏元素展示区域
  Widget _buildGameElementsSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(UXThemeConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '游戏元素',
              style: TextStyle(
                fontSize: UXThemeConfig.fontSizeL,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: UXThemeConfig.paddingM),
            
            // 进度条
            _buildProgressBars(),
            SizedBox(height: UXThemeConfig.paddingM),
            
            // 游戏按钮
            _buildGameButtons(),
            SizedBox(height: UXThemeConfig.paddingM),
            
            // 状态指示器
            _buildStatusIndicators(),
          ],
        ),
      ),
    );
  }

  /// 进度条
  Widget _buildProgressBars() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '进度条',
          style: TextStyle(
            fontSize: UXThemeConfig.fontSizeM,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: UXThemeConfig.paddingS),
        LinearProgressIndicator(
          value: 0.7,
          backgroundColor: UXThemeConfig.borderPrimary,
          valueColor: AlwaysStoppedAnimation<Color>(UXThemeConfig.successGreen),
        ),
        SizedBox(height: UXThemeConfig.paddingS),
        LinearProgressIndicator(
          value: 0.3,
          backgroundColor: UXThemeConfig.borderPrimary,
          valueColor: AlwaysStoppedAnimation<Color>(UXThemeConfig.warningOrange),
        ),
      ],
    );
  }

  /// 游戏按钮
  Widget _buildGameButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '游戏按钮',
          style: TextStyle(
            fontSize: UXThemeConfig.fontSizeM,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: UXThemeConfig.paddingS),
        Wrap(
          spacing: UXThemeConfig.paddingS,
          children: [
            _buildGameButton('开始', UXThemeConfig.successGreen, Icons.play_arrow),
            _buildGameButton('暂停', UXThemeConfig.warningOrange, Icons.pause),
            _buildGameButton('重试', UXThemeConfig.infoBlue, Icons.refresh),
            _buildGameButton('退出', UXThemeConfig.errorRed, Icons.stop),
          ],
        ),
      ],
    );
  }

  /// 游戏按钮
  Widget _buildGameButton(String label, Color color, IconData icon) {
    return ElevatedButton.icon(
      onPressed: () {},
      icon: Icon(icon, size: 18),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
        ),
      ),
    );
  }

  /// 状态指示器
  Widget _buildStatusIndicators() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '状态指示器',
          style: TextStyle(
            fontSize: UXThemeConfig.fontSizeM,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: UXThemeConfig.paddingS),
        Row(
          children: [
            _buildStatusChip('成功', UXThemeConfig.successGreen),
            SizedBox(width: UXThemeConfig.paddingS),
            _buildStatusChip('警告', UXThemeConfig.warningOrange),
            SizedBox(width: UXThemeConfig.paddingS),
            _buildStatusChip('错误', UXThemeConfig.errorRed),
            SizedBox(width: UXThemeConfig.paddingS),
            _buildStatusChip('信息', UXThemeConfig.infoBlue),
          ],
        ),
      ],
    );
  }

  /// 状态芯片
  Widget _buildStatusChip(String label, Color color) {
    return Chip(
      label: Text(
        label,
        style: TextStyle(
          color: Colors.white,
          fontSize: UXThemeConfig.fontSizeS,
        ),
      ),
      backgroundColor: color,
    );
  }

  /// 响应式展示区域
  Widget _buildResponsiveSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(UXThemeConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '响应式设计',
              style: TextStyle(
                fontSize: UXThemeConfig.fontSizeL,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: UXThemeConfig.paddingM),
            
            Text(
              '当前屏幕信息：',
              style: TextStyle(
                fontSize: UXThemeConfig.fontSizeM,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: UXThemeConfig.paddingS),
            
            _buildScreenInfo(),
          ],
        ),
      ),
    );
  }

  /// 屏幕信息
  Widget _buildScreenInfo() {
    final size = MediaQuery.of(context).size;
    final deviceType = _getDeviceType(size.width);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('宽度: ${size.width.toStringAsFixed(0)}px'),
        Text('高度: ${size.height.toStringAsFixed(0)}px'),
        Text('设备类型: $deviceType'),
        Text('像素密度: ${MediaQuery.of(context).devicePixelRatio}'),
      ],
    );
  }

  /// 获取设备类型
  String _getDeviceType(double width) {
    if (width < 600) return '手机';
    if (width < 900) return '平板';
    if (width < 1200) return '桌面';
    return '大屏';
  }
} 