import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

import '../../core/constants/app_constants.dart';
import '../../core/service_locator.dart';
import '../../domain/repositories/user_repository.dart';
import '../../core/theme/ux_theme_config.dart';

/// 启动页面 - 显示应用logo和初始化状态
class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> 
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  
  final Logger _logger = Logger();
  String _statusText = '正在启动逻辑实验室...';
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeApp();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeIn),
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 0.8, curve: Curves.elasticOut),
    ));

    _animationController.forward();
  }

  Future<void> _initializeApp() async {
    try {
      // 延迟一下让动画播放
      await Future.delayed(const Duration(milliseconds: 1000));

      // 检查是否有现有用户
      setState(() {
        _statusText = '检查用户数据...';
      });

      final userRepository = sl<UserRepository>();
      final currentUserResult = await userRepository.getCurrentUser();
      
      if (currentUserResult.isSuccess && currentUserResult.data != null) {
        final currentUser = currentUserResult.data!;
        setState(() {
          _statusText = '欢迎回来，${currentUser.nickname}！';
        });
        
        // 延迟后跳转到主页
        await Future.delayed(const Duration(milliseconds: 1500));
        
        if (mounted) {
          _logger.i('User found, navigating to home page');
          Navigator.pushReplacementNamed(context, '/home');
        }
      } else {
        setState(() {
          _statusText = '准备创建新用户...';
        });
        
        // 延迟后跳转到用户创建页面
        await Future.delayed(const Duration(milliseconds: 1500));
        
        if (mounted) {
          _logger.i('No user found, navigating to user selection page');
          Navigator.pushReplacementNamed(context, '/user_selection');
        }
      }

    } catch (e, stackTrace) {
      _logger.e('Failed to initialize app', error: e, stackTrace: stackTrace);
      
      setState(() {
        _hasError = true;
        _statusText = '启动遇到问题，请重试';
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: UXThemeConfig.primaryBlue,
      body: SafeArea(
        child: Column(
          children: [
            // 主要内容区域
            Expanded(
              child: Center(
                child: AnimatedBuilder(
                  animation: _animationController,
                  builder: (context, child) {
                    return FadeTransition(
                      opacity: _fadeAnimation,
                      child: ScaleTransition(
                        scale: _scaleAnimation,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Logo区域
                            Container(
                              width: 120,
                              height: 120,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(30),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.2),
                                    blurRadius: 20,
                                    offset: const Offset(0, 10),
                                  ),
                                ],
                              ),
                              child: const Icon(
                                Icons.psychology,
                                size: 60,
                                color: UXThemeConfig.primaryBlue,
                              ),
                            ),
                            
                            const SizedBox(height: 32),
                            
                            // 应用名称
                            Text(
                              AppConstants.appName,
                              style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 36,
                              ),
                            ),
                            
                            const SizedBox(height: 8),
                            
                            // 副标题
                            Text(
                              '逻辑思维训练专家',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: Colors.white.withOpacity(0.9),
                                fontSize: 18,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
            
            // 底部状态区域
            Padding(
              padding: const EdgeInsets.all(32),
              child: Column(
                children: [
                  // 加载指示器
                  if (!_hasError) ...[
                    const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ] else ...[
                    Icon(
                      Icons.error_outline,
                      color: Colors.white,
                      size: 24,
                    ),
                    const SizedBox(height: 16),
                  ],
                  
                  // 状态文本
                  Text(
                    _statusText,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white.withOpacity(0.9),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  // 错误时显示重试按钮
                  if (_hasError) ...[
                    const SizedBox(height: 24),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _hasError = false;
                          _statusText = '正在重新启动...';
                        });
                        _initializeApp();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: UXThemeConfig.primaryBlue,
                      ),
                      child: const Text('重试'),
                    ),
                  ],
                ],
              ),
            ),
            
            // 版本信息
            Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: Text(
                'v${AppConstants.appVersion}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.white.withOpacity(0.7),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 