import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/bloc_exports.dart';
import '../../core/service_locator.dart';

class UserCreationPage extends StatelessWidget {
  const UserCreationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<UserBloc>(),
      child: const UserCreationView(),
    );
  }
}

class UserCreationView extends StatefulWidget {
  const UserCreationView({super.key});

  @override
  State<UserCreationView> createState() => _UserCreationViewState();
}

class _UserCreationViewState extends State<UserCreationView> {
  final TextEditingController _nicknameController = TextEditingController();
  String _selectedAvatarId = 'avatar_1';
  String? _nicknameError;
  bool _isCheckingNickname = false;

  final List<String> _avatarIds = [
    'avatar_1', 'avatar_2', 'avatar_3', 'avatar_4',
    'avatar_5', 'avatar_6', 'avatar_7', 'avatar_8',
    'avatar_9', 'avatar_10', 'avatar_11', 'avatar_12',
  ];

  @override
  void dispose() {
    _nicknameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF6C5CE7),
              Color(0xFFA29BFE),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 自定义应用栏
              _buildAppBar(context),
              
              // 主要内容
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24.0),
                  child: BlocConsumer<UserBloc, UserState>(
                    listener: (context, state) {
                      if (state is UserCreated) {
                        // 用户创建成功，返回用户选择页面
                        Navigator.of(context).pop();
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('用户创建成功！'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      } else if (state is NicknameAvailabilityChecked) {
                        setState(() {
                          _isCheckingNickname = false;
                          _nicknameError = state.isAvailable ? null : '昵称已被使用';
                        });
                      } else if (state is UserError) {
                        setState(() {
                          _isCheckingNickname = false;
                        });
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(state.message),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    },
                    builder: (context, state) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 标题
                          Center(
                            child: Column(
                              children: [
                                Text(
                                  '创建新角色',
                                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  '选择一个头像并输入你的昵称',
                                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    color: Colors.white.withOpacity(0.8),
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 40),
                          
                          // 头像选择区域
                          _buildAvatarSection(),
                          const SizedBox(height: 32),
                          
                          // 昵称输入区域
                          _buildNicknameSection(),
                          const SizedBox(height: 40),
                          
                          // 创建按钮
                          _buildCreateButton(context, state),
                        ],
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.arrow_back_ios_new,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAvatarSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '选择头像',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1,
            ),
            itemCount: _avatarIds.length,
            itemBuilder: (context, index) {
              final avatarId = _avatarIds[index];
              final isSelected = avatarId == _selectedAvatarId;
              
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedAvatarId = avatarId;
                  });
                },
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _getAvatarColor(avatarId),
                    border: isSelected
                        ? Border.all(color: const Color(0xFF6C5CE7), width: 3)
                        : null,
                    boxShadow: isSelected
                        ? [
                            BoxShadow(
                              color: const Color(0xFF6C5CE7).withOpacity(0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ]
                        : [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                  ),
                  child: Icon(
                    _getAvatarIcon(avatarId),
                    size: 24,
                    color: Colors.white,
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildNicknameSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '输入昵称',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextField(
                controller: _nicknameController,
                decoration: InputDecoration(
                  hintText: '请输入1-12个字符的昵称',
                  hintStyle: TextStyle(color: Colors.grey[400]),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFF6C5CE7), width: 2),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Colors.red, width: 2),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Colors.red, width: 2),
                  ),
                  errorText: _nicknameError,
                  suffixIcon: _isCheckingNickname
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: Padding(
                            padding: EdgeInsets.all(12.0),
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6C5CE7)),
                            ),
                          ),
                        )
                      : null,
                ),
                maxLength: 12,
                onChanged: (value) {
                  // 实时验证昵称格式
                  setState(() {
                    if (value.isEmpty) {
                      _nicknameError = null;
                    } else if (value.isEmpty || value.length > 12) {
                      _nicknameError = '昵称长度应为1-12个字符';
                    } else if (!RegExp(r'^[a-zA-Z0-9\u4e00-\u9fa5_]+$').hasMatch(value)) {
                      _nicknameError = '昵称只能包含中文、英文、数字和下划线';
                    } else {
                      _nicknameError = null;
                      // 检查昵称可用性
                      _checkNicknameAvailability(value);
                    }
                  });
                },
              ),
              const SizedBox(height: 8),
              Text(
                '• 支持中文、英文、数字和下划线\n• 长度为1-12个字符\n• 昵称不能重复',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCreateButton(BuildContext context, UserState state) {
    final isLoading = state is UserCreating;
    final canCreate = _nicknameController.text.isNotEmpty &&
                     _nicknameError == null &&
                     !_isCheckingNickname &&
                     !isLoading;

    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: canCreate ? () => _createUser(context) : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.white,
          foregroundColor: const Color(0xFF6C5CE7),
          disabledBackgroundColor: Colors.white.withOpacity(0.5),
          disabledForegroundColor: Colors.grey,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 8,
          shadowColor: Colors.black.withOpacity(0.2),
        ),
        child: isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6C5CE7)),
                ),
              )
            : Text(
                '创建角色',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  void _checkNicknameAvailability(String nickname) {
    setState(() {
      _isCheckingNickname = true;
    });
    
    // 延迟检查，避免频繁请求
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted && _nicknameController.text == nickname) {
        context.read<UserBloc>().add(CheckNicknameAvailabilityEvent(nickname));
      }
    });
  }

  void _createUser(BuildContext context) {
    final nickname = _nicknameController.text.trim();
    if (nickname.isNotEmpty && _nicknameError == null) {
      context.read<UserBloc>().add(
        CreateUserEvent(
          nickname: nickname,
          avatarId: _selectedAvatarId,
        ),
      );
    }
  }

  Color _getAvatarColor(String avatarId) {
    switch (avatarId) {
      case 'avatar_1':
        return const Color(0xFF6C5CE7);
      case 'avatar_2':
        return const Color(0xFFA29BFE);
      case 'avatar_3':
        return const Color(0xFF74B9FF);
      case 'avatar_4':
        return const Color(0xFF00B894);
      case 'avatar_5':
        return const Color(0xFFE17055);
      case 'avatar_6':
        return const Color(0xFFE84393);
      case 'avatar_7':
        return const Color(0xFFFD79A8);
      case 'avatar_8':
        return const Color(0xFFFDCB6E);
      case 'avatar_9':
        return const Color(0xFF6C5CE7);
      case 'avatar_10':
        return const Color(0xFF00CEC9);
      case 'avatar_11':
        return const Color(0xFFB2BEC3);
      case 'avatar_12':
        return const Color(0xFF636E72);
      default:
        return const Color(0xFF6C5CE7);
    }
  }

  IconData _getAvatarIcon(String avatarId) {
    switch (avatarId) {
      case 'avatar_1':
        return Icons.child_care;
      case 'avatar_2':
        return Icons.face;
      case 'avatar_3':
        return Icons.pets;
      case 'avatar_4':
        return Icons.sports_esports;
      case 'avatar_5':
        return Icons.school;
      case 'avatar_6':
        return Icons.star;
      case 'avatar_7':
        return Icons.favorite;
      case 'avatar_8':
        return Icons.wb_sunny;
      case 'avatar_9':
        return Icons.rocket_launch;
      case 'avatar_10':
        return Icons.palette;
      case 'avatar_11':
        return Icons.music_note;
      case 'avatar_12':
        return Icons.sports_soccer;
      default:
        return Icons.person;
    }
  }
} 