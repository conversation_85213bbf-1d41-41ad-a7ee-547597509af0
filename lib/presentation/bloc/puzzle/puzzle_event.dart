import 'package:equatable/equatable.dart';

/// 谜题游戏相关事件基类
abstract class PuzzleEvent extends Equatable {
  const PuzzleEvent();

  @override
  List<Object?> get props => [];
}

/// 开始谜题游戏事件
class StartPuzzleEvent extends PuzzleEvent {
  final String levelId;
  final String userId;

  const StartPuzzleEvent({
    required this.levelId,
    required this.userId,
  });

  @override
  List<Object> get props => [levelId, userId];
}

/// 暂停游戏事件
class PausePuzzleEvent extends PuzzleEvent {
  const PausePuzzleEvent();
}

/// 恢复游戏事件
class ResumePuzzleEvent extends PuzzleEvent {
  const ResumePuzzleEvent();
}

/// 提交答案事件
class SubmitAnswerEvent extends PuzzleEvent {
  final dynamic userAnswer;
  final int timeSeconds;

  const SubmitAnswerEvent({
    required this.userAnswer,
    required this.timeSeconds,
  });

  @override
  List<Object?> get props => [userAnswer, timeSeconds];
}

/// 重新开始谜题事件
class RestartPuzzleEvent extends PuzzleEvent {
  const RestartPuzzleEvent();
}

/// 退出谜题事件
class ExitPuzzleEvent extends PuzzleEvent {
  final bool saveProgress;

  const ExitPuzzleEvent({this.saveProgress = true});

  @override
  List<Object> get props => [saveProgress];
}

/// 加载谜题列表事件
class LoadPuzzleListEvent extends PuzzleEvent {
  final String? themeWorld;
  final String? difficulty;

  const LoadPuzzleListEvent({
    this.themeWorld,
    this.difficulty,
  });

  @override
  List<Object?> get props => [themeWorld, difficulty];
}

/// 加载推荐谜题事件
class LoadRecommendedPuzzlesEvent extends PuzzleEvent {
  final String userId;

  const LoadRecommendedPuzzlesEvent(this.userId);

  @override
  List<Object> get props => [userId];
}

/// 更新游戏计时事件
class UpdateGameTimeEvent extends PuzzleEvent {
  final int seconds;

  const UpdateGameTimeEvent(this.seconds);

  @override
  List<Object> get props => [seconds];
}

/// 清除谜题错误事件
class ClearPuzzleErrorEvent extends PuzzleEvent {
  const ClearPuzzleErrorEvent();
} 