import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';

import '../../../domain/usecases/play_puzzle_usecase.dart';
import '../../../domain/usecases/save_progress_usecase.dart';
import '../../../domain/repositories/puzzle_repository.dart';
import '../../../domain/entities/puzzle_entity.dart';
import '../../../core/constants/app_constants.dart';
import 'puzzle_event.dart';
import 'puzzle_state.dart';

/// 谜题游戏状态管理BLoC
/// 
/// 负责处理谜题游戏相关的所有状态变化，包括：
/// - 谜题列表加载和推荐
/// - 谜题游戏会话管理
/// - 游戏计时和状态跟踪
/// - 答案验证和进度保存
class PuzzleBloc extends Bloc<PuzzleEvent, PuzzleState> {
  final PlayPuzzleUseCase _playPuzzleUseCase;
  final SaveProgressUseCase _saveProgressUseCase;
  final PuzzleRepository _puzzleRepository;
  final Logger _logger = Logger();

  // 游戏计时器
  Timer? _gameTimer;
  int _currentTimeSeconds = 0;
  int _hintsUsed = 0;

  PuzzleBloc({
    required PlayPuzzleUseCase playPuzzleUseCase,
    required SaveProgressUseCase saveProgressUseCase,
    required PuzzleRepository puzzleRepository,
  })  : _playPuzzleUseCase = playPuzzleUseCase,
        _saveProgressUseCase = saveProgressUseCase,
        _puzzleRepository = puzzleRepository,
        super(const PuzzleInitial()) {
    // 注册事件处理器
    on<StartPuzzleEvent>(_onStartPuzzle);
    on<PausePuzzleEvent>(_onPausePuzzle);
    on<ResumePuzzleEvent>(_onResumePuzzle);
    on<SubmitAnswerEvent>(_onSubmitAnswer);
    on<RestartPuzzleEvent>(_onRestartPuzzle);
    on<ExitPuzzleEvent>(_onExitPuzzle);
    on<LoadPuzzleListEvent>(_onLoadPuzzleList);
    on<LoadRecommendedPuzzlesEvent>(_onLoadRecommendedPuzzles);
    on<UpdateGameTimeEvent>(_onUpdateGameTime);
    on<ClearPuzzleErrorEvent>(_onClearPuzzleError);
  }

  @override
  Future<void> close() {
    _gameTimer?.cancel();
    return super.close();
  }

  /// 处理开始谜题游戏事件
  Future<void> _onStartPuzzle(
    StartPuzzleEvent event,
    Emitter<PuzzleState> emit,
  ) async {
    try {
      _logger.i('Starting puzzle game: ${event.levelId} for user: ${event.userId}');
      emit(const PuzzleGameLoading());

      // 使用PlayPuzzleUseCase开始游戏
      final result = await _playPuzzleUseCase(PlayPuzzleParams(
        levelId: event.levelId,
        userId: event.userId,
      ));

      if (result.isSuccess) {
        final gameResult = result.data!;
        emit(PuzzleGameLoaded(gameResult));

        // 自动开始游戏
        _startGameSession(gameResult, emit);
        _logger.i('Puzzle game started successfully: ${event.levelId}');
      } else {
        emit(PuzzleError(
          message: result.exception?.message ?? '启动游戏失败',
          errorCode: 'START_PUZZLE_ERROR',
        ));
      }
    } catch (e, stackTrace) {
      _logger.e('Failed to start puzzle game', error: e, stackTrace: stackTrace);
      emit(PuzzleError(
        message: '启动游戏失败：${e.toString()}',
        errorCode: 'START_PUZZLE_EXCEPTION',
      ));
    }
  }

  /// 处理暂停游戏事件
  Future<void> _onPausePuzzle(
    PausePuzzleEvent event,
    Emitter<PuzzleState> emit,
  ) async {
    if (state is PuzzleGameInProgress) {
      final gameState = state as PuzzleGameInProgress;
      _gameTimer?.cancel();
      
      emit(PuzzleGamePaused(gameState.copyWith(isPaused: true)));
      _logger.d('Puzzle game paused');
    }
  }

  /// 处理恢复游戏事件
  Future<void> _onResumePuzzle(
    ResumePuzzleEvent event,
    Emitter<PuzzleState> emit,
  ) async {
    if (state is PuzzleGamePaused) {
      final pausedState = state as PuzzleGamePaused;
      final gameState = pausedState.gameState.copyWith(isPaused: false);
      
      emit(gameState);
      _startTimer();
      _logger.d('Puzzle game resumed');
    }
  }

  /// 处理提交答案事件
  Future<void> _onSubmitAnswer(
    SubmitAnswerEvent event,
    Emitter<PuzzleState> emit,
  ) async {
    if (state is! PuzzleGameInProgress) return;

    final gameState = state as PuzzleGameInProgress;
    
    try {
      _logger.i('Submitting answer for puzzle: ${gameState.puzzle.levelId}');
      emit(PuzzleAnswerSubmitting(
        gameState: gameState,
        userAnswer: event.userAnswer,
      ));

      // 停止计时器
      _gameTimer?.cancel();

      // 验证答案
      final puzzleResult = await _puzzleRepository.getPuzzleById(gameState.puzzle.levelId);
      if (puzzleResult.isFailure || puzzleResult.data == null) {
        emit(PuzzleError(
          message: '无法找到谜题数据',
          errorCode: 'PUZZLE_NOT_FOUND',
        ));
        return;
      }
      
      final puzzle = puzzleResult.data!;
      final validationResult = await _puzzleRepository.validateAnswer(puzzle, event.userAnswer);
      final isCorrect = validationResult.isSuccess && validationResult.data == true;

      if (isCorrect) {
        await _handleCorrectAnswer(gameState, event.userAnswer, event.timeSeconds, emit);
      } else {
        await _handleIncorrectAnswer(gameState, event.userAnswer, emit);
      }
    } catch (e, stackTrace) {
      _logger.e('Failed to submit answer', error: e, stackTrace: stackTrace);
      emit(PuzzleError(
        message: '提交答案失败：${e.toString()}',
        errorCode: 'SUBMIT_ANSWER_ERROR',
      ));
    }
  }

  /// 处理重新开始谜题事件
  Future<void> _onRestartPuzzle(
    RestartPuzzleEvent event,
    Emitter<PuzzleState> emit,
  ) async {
    if (state is PuzzleGameInProgress || state is PuzzleGamePaused) {
      final gameState = state is PuzzleGameInProgress 
          ? state as PuzzleGameInProgress
          : (state as PuzzleGamePaused).gameState;

      _resetGameSession();
      emit(PuzzleGameInProgress(
        puzzle: gameState.puzzle,
        user: gameState.user,
        currentTimeSeconds: 0,
        attempts: gameState.attempts + 1,
      ));
      
      _startTimer();
      _logger.d('Puzzle game restarted');
    }
  }

  /// 处理退出谜题事件
  Future<void> _onExitPuzzle(
    ExitPuzzleEvent event,
    Emitter<PuzzleState> emit,
  ) async {
    try {
      _gameTimer?.cancel();

      if (event.saveProgress && (state is PuzzleGameInProgress || state is PuzzleGamePaused)) {
        // TODO: 保存游戏进度到本地存储
        _logger.d('Saving game progress before exit');
      }

      emit(const PuzzleInitial());
      _logger.d('Exited puzzle game');
    } catch (e, stackTrace) {
      _logger.e('Failed to exit puzzle', error: e, stackTrace: stackTrace);
      emit(PuzzleError(
        message: '退出游戏失败：${e.toString()}',
        errorCode: 'EXIT_PUZZLE_ERROR',
      ));
    }
  }

  /// 处理加载谜题列表事件
  Future<void> _onLoadPuzzleList(
    LoadPuzzleListEvent event,
    Emitter<PuzzleState> emit,
  ) async {
    try {
      _logger.i('Loading puzzle list');
      emit(const PuzzleListLoading());

      List<PuzzleEntity> puzzles;
      if (event.themeWorld != null) {
        // 将字符串转换为ThemeWorld枚举
        final themeWorld = ThemeWorld.values.firstWhere(
          (tw) => tw.id == event.themeWorld,
          orElse: () => ThemeWorld.forest,
        );
        final puzzlesResult = await _puzzleRepository.getPuzzlesByThemeWorld(themeWorld);
        puzzles = puzzlesResult.getOrThrow();
      } else {
        final puzzlesResult = await _puzzleRepository.getAllPuzzles();
        puzzles = puzzlesResult.getOrThrow();
      }

      emit(PuzzleListLoaded(puzzles: puzzles));
      _logger.i('Puzzle list loaded successfully: ${puzzles.length} puzzles');
    } catch (e, stackTrace) {
      _logger.e('Failed to load puzzle list', error: e, stackTrace: stackTrace);
      emit(PuzzleError(
        message: '加载谜题列表失败：${e.toString()}',
        errorCode: 'LOAD_PUZZLE_LIST_ERROR',
      ));
    }
  }

  /// 处理加载推荐谜题事件
  Future<void> _onLoadRecommendedPuzzles(
    LoadRecommendedPuzzlesEvent event,
    Emitter<PuzzleState> emit,
  ) async {
    try {
      _logger.i('Loading recommended puzzles for user: ${event.userId}');

      final recommendedPuzzlesResult = await _puzzleRepository.getRecommendedPuzzles(
        userSkillPoints: {}, // TODO: 从用户数据获取技能点数
        completedLevels: [], // TODO: 从用户数据获取已完成关卡
        limit: 10,
      );
      
      final recommendedPuzzles = recommendedPuzzlesResult.getOrThrow();

      if (state is PuzzleListLoaded) {
        final currentState = state as PuzzleListLoaded;
        emit(PuzzleListLoaded(
          puzzles: currentState.puzzles,
          recommendedPuzzles: recommendedPuzzles,
        ));
      } else {
        emit(PuzzleListLoaded(
          puzzles: [],
          recommendedPuzzles: recommendedPuzzles,
        ));
      }

      _logger.i('Recommended puzzles loaded: ${recommendedPuzzles.length} puzzles');
    } catch (e, stackTrace) {
      _logger.e('Failed to load recommended puzzles', error: e, stackTrace: stackTrace);
      emit(PuzzleError(
        message: '加载推荐谜题失败：${e.toString()}',
        errorCode: 'LOAD_RECOMMENDED_PUZZLES_ERROR',
      ));
    }
  }

  /// 处理更新游戏时间事件
  Future<void> _onUpdateGameTime(
    UpdateGameTimeEvent event,
    Emitter<PuzzleState> emit,
  ) async {
    if (state is PuzzleGameInProgress) {
      final gameState = state as PuzzleGameInProgress;
      emit(gameState.copyWith(currentTimeSeconds: event.seconds));
    }
  }

  /// 处理清除谜题错误事件
  Future<void> _onClearPuzzleError(
    ClearPuzzleErrorEvent event,
    Emitter<PuzzleState> emit,
  ) async {
    _logger.d('Clearing puzzle error');
    emit(const PuzzleInitial());
  }

  /// 开始游戏会话
  void _startGameSession(dynamic gameResult, Emitter<PuzzleState> emit) {
    _resetGameSession();
    
    emit(PuzzleGameInProgress(
      puzzle: gameResult.puzzle,
      user: gameResult.user,
      currentTimeSeconds: 0,
      attempts: 1,
      currentGameState: gameResult.savedState,
    ));

    _startTimer();
  }

  /// 启动计时器
  void _startTimer() {
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _currentTimeSeconds++;
      add(UpdateGameTimeEvent(_currentTimeSeconds));
    });
  }

  /// 重置游戏会话
  void _resetGameSession() {
    _gameTimer?.cancel();
    _currentTimeSeconds = 0;
    _hintsUsed = 0;
  }

  /// 处理正确答案
  Future<void> _handleCorrectAnswer(
    PuzzleGameInProgress gameState,
    dynamic userAnswer,
    int timeSeconds,
    Emitter<PuzzleState> emit,
  ) async {
    try {
      // 使用SaveProgressUseCase保存进度
      final result = await _saveProgressUseCase(SaveProgressParams(
        userId: gameState.user.id,
        levelId: gameState.puzzle.levelId,
        completed: true,
        score: gameState.puzzle.calculateScore(
          timeSeconds: timeSeconds,
          hintsUsed: _hintsUsed,
          isCorrect: true,
        ),
        timeSeconds: timeSeconds,
        hintsUsed: _hintsUsed,
        attempts: gameState.attempts,
        userAnswer: userAnswer,
      ));

      if (result.isSuccess) {
        final progressResult = result.data!;
        
        emit(PuzzleAnswerCorrect(
          puzzle: gameState.puzzle,
          user: progressResult.updatedUser,
          finalScore: progressResult.levelProgress.bestScore,
          timeSeconds: timeSeconds,
          hintsUsed: _hintsUsed,
          starRating: progressResult.levelProgress.starRating,
          newAchievements: progressResult.newAchievements,
          skillPointsEarned: progressResult.skillPointsEarned,
          isNewRecord: progressResult.isNewRecord,
        ));

        _logger.i('Puzzle completed successfully with score: ${progressResult.levelProgress.bestScore}');
      } else {
        emit(PuzzleError(
          message: result.exception?.message ?? '保存进度失败',
          errorCode: 'SAVE_PROGRESS_ERROR',
        ));
      }
    } catch (e, stackTrace) {
      _logger.e('Failed to handle correct answer', error: e, stackTrace: stackTrace);
      emit(PuzzleError(
        message: '保存游戏结果失败：${e.toString()}',
        errorCode: 'HANDLE_CORRECT_ANSWER_ERROR',
      ));
    }
  }

  /// 处理错误答案
  Future<void> _handleIncorrectAnswer(
    PuzzleGameInProgress gameState,
    dynamic userAnswer,
    Emitter<PuzzleState> emit,
  ) async {
    // 生成反馈信息
    final feedback = await _generateAnswerFeedback(gameState.puzzle, userAnswer);
    
    emit(PuzzleAnswerIncorrect(
      gameState: gameState.copyWith(attempts: gameState.attempts + 1),
      userAnswer: userAnswer,
      feedback: feedback,
    ));

    _logger.d('Incorrect answer submitted for puzzle: ${gameState.puzzle.levelId}');

    // 3秒后自动恢复游戏
    Timer(const Duration(seconds: 3), () {
      if (state is PuzzleAnswerIncorrect) {
        final incorrectState = state as PuzzleAnswerIncorrect;
        emit(incorrectState.gameState);
        _startTimer();
      }
    });
  }

  /// 生成答案反馈
  Future<String?> _generateAnswerFeedback(dynamic puzzle, dynamic userAnswer) async {
    // TODO: 根据谜题类型和用户答案生成具体的反馈信息
    return '答案不正确，请再试一次！';
  }

  /// 增加提示使用次数
  void incrementHintsUsed() {
    _hintsUsed++;
  }

  /// 获取当前游戏时间
  int get currentGameTime => _currentTimeSeconds;

  /// 获取已使用的提示次数
  int get hintsUsed => _hintsUsed;
} 