import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';

import '../../../domain/usecases/generate_hint_usecase.dart';
import 'hint_event.dart';
import 'hint_state.dart';

/// 提示系统状态管理BLoC
/// 
/// 负责处理提示系统相关的所有状态变化，包括：
/// - 提示可用性检查
/// - 提示生成和显示
/// - 提示冷却时间管理
/// - 提示使用次数限制
class HintBloc extends Bloc<HintEvent, HintState> {
  final GenerateHintUseCase _generateHintUseCase;
  final Logger _logger = Logger();

  // 冷却计时器
  Timer? _cooldownTimer;

  HintBloc({
    required GenerateHintUseCase generateHintUseCase,
  })  : _generateHintUseCase = generateHintUseCase,
        super(const HintInitial()) {
    // 注册事件处理器
    on<RequestHintEvent>(_onRequestHint);
    on<ShowHintEvent>(_onShowHint);
    on<HideHintEvent>(_onHideHint);
    on<ResetHintEvent>(_onResetHint);
    on<CheckHintAvailabilityEvent>(_onCheckHintAvailability);
    on<ClearHintErrorEvent>(_onClearHintError);
  }

  @override
  Future<void> close() {
    _cooldownTimer?.cancel();
    return super.close();
  }

  /// 处理请求提示事件
  Future<void> _onRequestHint(
    RequestHintEvent event,
    Emitter<HintState> emit,
  ) async {
    try {
      _logger.i('Requesting hint for level: ${event.levelId}, level: ${event.currentHintLevel}');
      emit(const HintGenerating());

      // 使用GenerateHintUseCase生成提示
      final result = await _generateHintUseCase(GenerateHintParams(
        userId: event.userId,
        levelId: event.levelId,
        hintLevel: event.currentHintLevel,
        currentGameState: event.currentGameState ?? {},
      ));

      if (result.isSuccess) {
        final hintResult = result.data!;
        emit(HintGenerated(hintResult));

        // 自动显示提示
        add(ShowHintEvent(
          hintText: hintResult.hintText,
          hintLevel: event.currentHintLevel,
        ));

        // 启动冷却计时器（暂时使用固定值）
        const cooldownSeconds = 30; // TODO: 从配置获取
        if (cooldownSeconds > 0) {
          _startCooldownTimer(cooldownSeconds, emit);
        }

        _logger.i('Hint generated successfully: ${hintResult.hintType}');
      } else {
        emit(HintError(
          message: result.exception?.message ?? '生成提示失败',
          errorCode: 'GENERATE_HINT_ERROR',
        ));
      }
    } catch (e, stackTrace) {
      _logger.e('Failed to request hint', error: e, stackTrace: stackTrace);
      emit(HintError(
        message: '生成提示失败：${e.toString()}',
        errorCode: 'REQUEST_HINT_EXCEPTION',
      ));
    }
  }

  /// 处理显示提示事件
  Future<void> _onShowHint(
    ShowHintEvent event,
    Emitter<HintState> emit,
  ) async {
    if (state is HintGenerated) {
      final generatedState = state as HintGenerated;
      
      emit(HintShowing(
        hintText: event.hintText,
        hintLevel: event.hintLevel,
        hintType: generatedState.hintType,
        remainingHints: generatedState.remainingHints,
        isLastHint: generatedState.isLastHint,
      ));

      _logger.d('Hint shown: level ${event.hintLevel}');
    }
  }

  /// 处理隐藏提示事件
  Future<void> _onHideHint(
    HideHintEvent event,
    Emitter<HintState> emit,
  ) async {
    emit(const HintHidden());
    _logger.d('Hint hidden');
  }

  /// 处理重置提示状态事件
  Future<void> _onResetHint(
    ResetHintEvent event,
    Emitter<HintState> emit,
  ) async {
    _cooldownTimer?.cancel();
    emit(const HintInitial());
    _logger.d('Hint state reset');
  }

  /// 处理检查提示可用性事件
  Future<void> _onCheckHintAvailability(
    CheckHintAvailabilityEvent event,
    Emitter<HintState> emit,
  ) async {
    try {
      _logger.d('Checking hint availability for level: ${event.levelId}');
      emit(const HintAvailabilityChecking());

      // 这里应该调用Repository或UseCase来检查提示可用性
      // 暂时使用模拟数据
      await Future.delayed(const Duration(milliseconds: 500));

      // TODO: 实现真实的提示可用性检查逻辑
      const isAvailable = true;
      const remainingHints = 3;
      const cooldownSeconds = 0;

      emit(const HintAvailabilityChecked(
        isAvailable: isAvailable,
        remainingHints: remainingHints,
        cooldownSeconds: cooldownSeconds,
      ));

      _logger.d('Hint availability checked: available=$isAvailable, remaining=$remainingHints');
    } catch (e, stackTrace) {
      _logger.e('Failed to check hint availability', error: e, stackTrace: stackTrace);
      emit(HintError(
        message: '检查提示可用性失败：${e.toString()}',
        errorCode: 'CHECK_HINT_AVAILABILITY_ERROR',
      ));
    }
  }

  /// 处理清除提示错误事件
  Future<void> _onClearHintError(
    ClearHintErrorEvent event,
    Emitter<HintState> emit,
  ) async {
    _logger.d('Clearing hint error');
    emit(const HintInitial());
  }

  /// 启动冷却计时器
  void _startCooldownTimer(int totalSeconds, Emitter<HintState> emit) {
    _cooldownTimer?.cancel();
    
    int remainingSeconds = totalSeconds;
    emit(HintCooldown(
      remainingSeconds: remainingSeconds,
      totalCooldownSeconds: totalSeconds,
    ));

    _cooldownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      remainingSeconds--;
      
      if (remainingSeconds <= 0) {
        timer.cancel();
        emit(const HintInitial());
        _logger.d('Hint cooldown completed');
      } else {
        emit(HintCooldown(
          remainingSeconds: remainingSeconds,
          totalCooldownSeconds: totalSeconds,
        ));
      }
    });

    _logger.d('Started hint cooldown timer: ${totalSeconds}s');
  }

  /// 检查当前是否可以请求提示
  bool get canRequestHint {
    return state is! HintGenerating && 
           state is! HintCooldown && 
           state is! HintError;
  }

  /// 获取当前提示状态信息
  Map<String, dynamic> get currentHintInfo {
    if (state is HintShowing) {
      final showingState = state as HintShowing;
      return {
        'isShowing': true,
        'hintText': showingState.hintText,
        'hintLevel': showingState.hintLevel,
        'hintType': showingState.hintType,
        'remainingHints': showingState.remainingHints,
        'isLastHint': showingState.isLastHint,
      };
    } else if (state is HintCooldown) {
      final cooldownState = state as HintCooldown;
      return {
        'isShowing': false,
        'inCooldown': true,
        'remainingSeconds': cooldownState.remainingSeconds,
        'cooldownProgress': cooldownState.cooldownProgress,
      };
    } else {
      return {
        'isShowing': false,
        'inCooldown': false,
      };
    }
  }
} 