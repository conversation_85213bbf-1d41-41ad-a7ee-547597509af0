import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';

import '../../../core/error/failures.dart';
import '../../../domain/usecases/create_user_usecase.dart';
import '../../../domain/repositories/user_repository.dart';
import 'user_event.dart';
import 'user_state.dart';

/// 用户状态管理BLoC
///
/// 负责处理用户相关的所有状态变化，包括：
/// - 用户列表加载和管理
/// - 用户创建、更新、删除
/// - 用户选择和切换
/// - 昵称可用性检查
class UserBloc extends Bloc<UserEvent, UserState> {
  final CreateUserUseCase _createUserUseCase;
  final UserRepository _userRepository;
  final Logger _logger = Logger();

  UserBloc({
    required CreateUserUseCase createUserUseCase,
    required UserRepository userRepository,
  })  : _createUserUseCase = createUserUseCase,
        _userRepository = userRepository,
        super(const UserInitial()) {
    // 注册事件处理器
    on<LoadUsersEvent>(_onLoadUsers);
    on<SelectUserEvent>(_onSelectUser);
    on<CreateUserEvent>(_onCreateUser);
    on<UpdateUserEvent>(_onUpdateUser);
    on<DeleteUserEvent>(_onDeleteUser);
    on<SwitchUserEvent>(_onSwitchUser);
    on<CheckNicknameAvailabilityEvent>(_onCheckNicknameAvailability);
    on<ClearUserErrorEvent>(_onClearUserError);
  }

  /// 处理加载所有用户事件
  Future<void> _onLoadUsers(
    LoadUsersEvent event,
    Emitter<UserState> emit,
  ) async {
    try {
      _logger.i('Loading all users');
      emit(const UserLoading());

      final usersResult = await _userRepository.getAllUsers();
      final currentUserResult = await _userRepository.getCurrentUser();

      final users = usersResult.getOrElse(() => []);
      final currentUser = currentUserResult.data;

      emit(UsersLoaded(
        users: users,
        currentUser: currentUser,
      ));

      _logger.i('Users loaded successfully: ${users.length} users');
    } on Failure catch (f) {
      _logger.e('A failure occurred while loading users', error: f);
      emit(UserError(message: f.message, errorCode: f.code));
    } catch (e, stackTrace) {
      _logger.e('An unexpected error occurred while loading users', error: e, stackTrace: stackTrace);
      emit(UserError(message: '加载用户列表失败: $e'));
    }
  }

  /// 处理选择用户事件
  Future<void> _onSelectUser(
    SelectUserEvent event,
    Emitter<UserState> emit,
  ) async {
    try {
      _logger.i('Selecting user: ${event.userId}');

      final userResult = await _userRepository.getUserById(event.userId);
      final user = userResult.data;

      if (user == null) {
        emit(const UserError(
          message: '用户不存在',
          errorCode: 'USER_NOT_FOUND',
        ));
        return;
      }

      await _userRepository.setCurrentUser(event.userId);

      emit(UserSelected(user));
      _logger.i('User selected successfully: ${user.nickname}');
    } on Failure catch (f) {
      _logger.e('A failure occurred while selecting user', error: f);
      emit(UserError(message: f.message, errorCode: f.code));
    } catch (e, stackTrace) {
      _logger.e('An unexpected error occurred while selecting user', error: e, stackTrace: stackTrace);
      emit(UserError(message: '选择用户失败: $e'));
    }
  }

  /// 处理创建用户事件
  Future<void> _onCreateUser(
    CreateUserEvent event,
    Emitter<UserState> emit,
  ) async {
    try {
      _logger.i('Creating user: ${event.nickname}');
      emit(const UserCreating());

      final result = await _createUserUseCase(CreateUserParams(
        nickname: event.nickname,
        avatarId: event.avatarId,
      ));

      if (result.isSuccess) {
        final user = result.data!;
        emit(UserCreated(user));
        _logger.i('User created successfully: ${user.id}');
        add(const LoadUsersEvent());
      } else {
        emit(UserError(
          message: result.exception?.message ?? '创建用户失败',
          errorCode: 'CREATE_USER_ERROR',
        ));
      }
    } catch (e, stackTrace) {
      _logger.e('An unexpected error occurred while creating user', error: e, stackTrace: stackTrace);
      emit(UserError(message: '创建用户时发生意外错误: $e'));
    }
  }

  /// 处理更新用户事件
  Future<void> _onUpdateUser(
    UpdateUserEvent event,
    Emitter<UserState> emit,
  ) async {
    try {
      _logger.i('Updating user: ${event.userId}');
      emit(const UserUpdating());

      final existingUserResult = await _userRepository.getUserById(event.userId);
      final existingUser = existingUserResult.data;

      if (existingUser == null) {
        emit(const UserError(message: '用户不存在', errorCode: 'USER_NOT_FOUND'));
        return;
      }

      final updatedUser = existingUser.copyWith(
        nickname: event.nickname,
        avatarId: event.avatarId,
      );

      await _userRepository.updateUser(updatedUser);

      emit(UserUpdated(updatedUser));
      _logger.i('User updated successfully: ${updatedUser.id}');
      add(const LoadUsersEvent());
    } on Failure catch (f) {
      _logger.e('A failure occurred while updating user', error: f);
      emit(UserError(message: f.message, errorCode: f.code));
    } catch (e, stackTrace) {
      _logger.e('An unexpected error occurred while updating user', error: e, stackTrace: stackTrace);
      emit(UserError(message: '更新用户失败: $e'));
    }
  }

  /// 处理删除用户事件
  Future<void> _onDeleteUser(
    DeleteUserEvent event,
    Emitter<UserState> emit,
  ) async {
    try {
      _logger.i('Deleting user: ${event.userId}');
      emit(const UserDeleting());

      await _userRepository.deleteUser(event.userId);

      emit(UserDeleted(event.userId));
      _logger.i('User deleted successfully: ${event.userId}');
      add(const LoadUsersEvent());
    } on Failure catch (f) {
      _logger.e('A failure occurred while deleting user', error: f);
      emit(UserError(message: f.message, errorCode: f.code));
    } catch (e, stackTrace) {
      _logger.e('An unexpected error occurred while deleting user', error: e, stackTrace: stackTrace);
      emit(UserError(message: '删除用户失败: $e'));
    }
  }

  /// 处理切换用户事件
  Future<void> _onSwitchUser(
    SwitchUserEvent event,
    Emitter<UserState> emit,
  ) async {
    try {
      _logger.i('Switching to user: ${event.userId}');

      final userResult = await _userRepository.getUserById(event.userId);
      final user = userResult.data;

      if (user == null) {
        emit(const UserError(message: '用户不存在', errorCode: 'USER_NOT_FOUND'));
        return;
      }

      await _userRepository.setCurrentUser(event.userId);

      emit(UserOperationSuccess(
        message: '已切换到用户：${user.nickname}',
        user: user,
      ));
      _logger.i('Switched to user successfully: ${user.nickname}');
      add(const LoadUsersEvent());
    } on Failure catch (f) {
      _logger.e('A failure occurred while switching user', error: f);
      emit(UserError(message: f.message, errorCode: f.code));
    } catch (e, stackTrace) {
      _logger.e('An unexpected error occurred while switching user', error: e, stackTrace: stackTrace);
      emit(UserError(message: '切换用户失败: $e'));
    }
  }

  /// 处理检查昵称可用性事件
  Future<void> _onCheckNicknameAvailability(
    CheckNicknameAvailabilityEvent event,
    Emitter<UserState> emit,
  ) async {
    try {
      _logger.d('Checking nickname availability: ${event.nickname}');
      emit(const CheckingNicknameAvailability());

      final isExists = await _userRepository.isNicknameExists(event.nickname);

      emit(NicknameAvailabilityChecked(
        nickname: event.nickname,
        isAvailable: !isExists.getOrElse(() => true), // 默认不可用以防出错
      ));

      _logger.d('Nickname availability checked: ${event.nickname} -> ${!isExists.getOrElse(() => true)}');
    } on Failure catch (f) {
      _logger.e('A failure occurred while checking nickname', error: f);
      emit(UserError(message: f.message, errorCode: f.code));
    } catch (e, stackTrace) {
      _logger.e('An unexpected error occurred while checking nickname', error: e, stackTrace: stackTrace);
      emit(UserError(message: '检查昵称可用性失败: $e'));
    }
  }

  /// 处理清除用户错误事件
  Future<void> _onClearUserError(
    ClearUserErrorEvent event,
    Emitter<UserState> emit,
  ) async {
    _logger.d('Clearing user error');
    if (state is UserError) {
      add(const LoadUsersEvent());
    }
  }
} 