import 'package:flutter/material.dart';
import '../../data/models/puzzle.dart';

class PuzzleGridWidget extends StatelessWidget {
  final GraphicPatternData puzzleData;
  final String? selectedOption;
  final Function(int)? onGridTap;

  const PuzzleGridWidget({
    super.key,
    required this.puzzleData,
    this.selectedOption,
    this.onGridTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: AspectRatio(
          aspectRatio: 1.0,
          child: GridView.builder(
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: 9,
            itemBuilder: (context, index) {
              return _buildGridCell(context, index);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildGridCell(BuildContext context, int index) {
    final isEmptyCell = puzzleData.emptyIndex == index;
    final cellContent = puzzleData.grid[index];

    return GestureDetector(
      onTap: isEmptyCell && selectedOption != null ? () => onGridTap?.call(index) : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: isEmptyCell 
              ? (selectedOption != null 
                  ? const Color(0xFF6C5CE7).withOpacity(0.1) 
                  : Colors.grey[100])
              : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isEmptyCell 
                ? (selectedOption != null 
                    ? const Color(0xFF6C5CE7) 
                    : Colors.grey[300]!)
                : Colors.grey[200]!,
            width: isEmptyCell ? 2 : 1,
          ),
          boxShadow: [
            if (!isEmptyCell)
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
          ],
        ),
        child: isEmptyCell ? _buildEmptyCell() : _buildContentCell(cellContent),
      ),
    );
  }

  Widget _buildEmptyCell() {
    return Container(
      child: selectedOption != null
          ? Stack(
              children: [
                Center(
                  child: _buildShapeWidget(selectedOption!, isPreview: true),
                ),
                Positioned(
                  top: 4,
                  right: 4,
                  child: Container(
                    width: 20,
                    height: 20,
                    decoration: const BoxDecoration(
                      color: Color(0xFF6C5CE7),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.question_mark,
                      color: Colors.white,
                      size: 12,
                    ),
                  ),
                ),
              ],
            )
          : const Center(
              child: Icon(
                Icons.help_outline,
                color: Colors.grey,
                size: 32,
              ),
            ),
    );
  }

  Widget _buildContentCell(String? content) {
    if (content == null) return Container();
    
    return Center(
      child: _buildShapeWidget(content),
    );
  }

  Widget _buildShapeWidget(String shapeId, {bool isPreview = false}) {
    // 根据shapeId返回对应的图形
    // 这里使用简单的图形示例，实际项目中可以使用SVG或自定义绘制
    return Container(
      width: isPreview ? 30 : 40,
      height: isPreview ? 30 : 40,
      decoration: BoxDecoration(
        color: _getShapeColor(shapeId).withOpacity(isPreview ? 0.6 : 1.0),
        shape: _getShapeType(shapeId),
        borderRadius: _getShapeType(shapeId) == BoxShape.rectangle 
            ? BorderRadius.circular(8) 
            : null,
      ),
      child: _getShapeIcon(shapeId, isPreview),
    );
  }

  Color _getShapeColor(String shapeId) {
    switch (shapeId.toLowerCase()) {
      case 'red_circle':
      case 'red_square':
      case 'red_triangle':
        return Colors.red;
      case 'blue_circle':
      case 'blue_square':
      case 'blue_triangle':
        return Colors.blue;
      case 'green_circle':
      case 'green_square':
      case 'green_triangle':
        return Colors.green;
      case 'yellow_circle':
      case 'yellow_square':
      case 'yellow_triangle':
        return Colors.yellow;
      case 'purple_circle':
      case 'purple_square':
      case 'purple_triangle':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  BoxShape _getShapeType(String shapeId) {
    if (shapeId.toLowerCase().contains('circle')) {
      return BoxShape.circle;
    }
    return BoxShape.rectangle;
  }

  Widget? _getShapeIcon(String shapeId, bool isPreview) {
    if (shapeId.toLowerCase().contains('triangle')) {
      return Icon(
        Icons.change_history,
        color: Colors.white,
        size: isPreview ? 16 : 20,
      );
    }
    return null;
  }
} 