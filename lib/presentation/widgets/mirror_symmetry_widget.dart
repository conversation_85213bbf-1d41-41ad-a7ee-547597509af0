import 'package:flutter/material.dart';
import '../../core/theme/ux_theme_config.dart';

/// 镜像对称游戏组件
/// 
/// 这是一个完整的镜像对称游戏实现，包含：
/// - 原始衣服展示
/// - 镜子动画效果
/// - 选项展示和选择
/// - 答案验证和反馈
/// - 详细解析展示
class MirrorSymmetryWidget extends StatefulWidget {
  /// 题目数据，包含原始图案和选项
  final Map<String, dynamic> puzzleData;
  
  /// 答案选择回调
  final Function(String selectedAnswer) onAnswerSelected;
  
  /// 提示请求回调
  final VoidCallback? onHintRequested;
  
  /// 重新开始回调
  final VoidCallback? onRestart;

  const MirrorSymmetryWidget({
    super.key,
    required this.puzzleData,
    required this.onAnswerSelected,
    this.onHintRequested,
    this.onRestart,
  });

  @override
  State<MirrorSymmetryWidget> createState() => _MirrorSymmetryWidgetState();
}

class _MirrorSymmetryWidgetState extends State<MirrorSymmetryWidget>
    with TickerProviderStateMixin {
  // 动画控制器
  late AnimationController _mirrorAnimation;
  late AnimationController _selectionAnimation;
  late AnimationController _feedbackAnimation;
  
  // 游戏状态
  String? _selectedAnswer;
  bool _showFeedback = false;
  bool _showExplanation = false;
  bool _isCorrect = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    // 镜子反射动画
    _mirrorAnimation = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    // 选择动画
    _selectionAnimation = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // 反馈动画
    _feedbackAnimation = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _mirrorAnimation.dispose();
    _selectionAnimation.dispose();
    _feedbackAnimation.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(UXThemeConfig.paddingL),
      child: Column(
        children: [
          // 游戏说明
          _buildInstructions(),
          
          SizedBox(height: UXThemeConfig.paddingXL),
          
          // 主要游戏区域或答案解析
          Expanded(
            flex: 3,
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              transitionBuilder: (Widget child, Animation<double> animation) {
                // 组合动画：淡入淡出 + 滑动效果
                return FadeTransition(
                  opacity: animation,
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0.3, 0),
                      end: Offset.zero,
                    ).animate(CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeInOut,
                    )),
                    child: child,
                  ),
                );
              },
              child: _showExplanation 
                  ? _buildAnswerExplanation()
                  : _buildGameArea(),
            ),
          ),
          
          SizedBox(height: UXThemeConfig.paddingL),
          
          // 选项区域或解析步骤
          Expanded(
            flex: 2,
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              transitionBuilder: (Widget child, Animation<double> animation) {
                // 组合动画：淡入淡出 + 缩放效果
                return FadeTransition(
                  opacity: animation,
                  child: ScaleTransition(
                    scale: Tween<double>(
                      begin: 0.8,
                      end: 1.0,
                    ).animate(CurvedAnimation(
                      parent: animation,
                      curve: Curves.elasticOut,
                    )),
                    child: child,
                  ),
                );
              },
              child: _showExplanation
                  ? _buildExplanationSteps()
                  : _buildOptionsArea(),
            ),
          ),
          
          SizedBox(height: UXThemeConfig.paddingL),
          
          // 按钮区域
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildInstructions() {
    return Container(
      padding: EdgeInsets.all(UXThemeConfig.paddingM),
      decoration: BoxDecoration(
        color: UXThemeConfig.accentBlue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
        border: Border.all(
          color: UXThemeConfig.accentBlue.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.help_outline,
            color: UXThemeConfig.accentBlue,
            size: 24,
          ),
          SizedBox(width: UXThemeConfig.paddingS),
          Expanded(
            child: Text(
              '小亚照镜子时发现图案的位置发生了变化。请选择通过镜子看到的正确衣服。',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: UXThemeConfig.accentBlue,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGameArea() {
    return Row(
      children: [
        // 原始衣服
        Expanded(
          child: _buildOriginalClothes(),
        ),
        
        // 镜子和箭头
        SizedBox(
          width: 80,
          child: _buildMirrorSection(),
        ),
        
        // 镜像后的衣服轮廓
        Expanded(
          child: _buildMirrorFrame(),
        ),
      ],
    );
  }

  Widget _buildOriginalClothes() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '原始衣服',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: UXThemeConfig.textSecondary,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: UXThemeConfig.paddingM),
          
          // 衣服图案
          Container(
            width: 120,
            height: 160,
            decoration: BoxDecoration(
              color: _getClothesColor(),
              borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
              border: Border.all(
                color: UXThemeConfig.borderPrimary,
                width: 2,
              ),
            ),
            child: Stack(
              children: [
                // 衣服基本形状
                _buildClothesShape(),
                // 图案
                _buildPattern(isOriginal: true),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMirrorSection() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 箭头
        AnimatedBuilder(
          animation: _mirrorAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: 1.0 + (_mirrorAnimation.value * 0.2),
              child: Icon(
                Icons.arrow_forward,
                size: 32,
                color: UXThemeConfig.primaryBlue.withValues(
                  alpha: 0.6 + (_mirrorAnimation.value * 0.4),
                ),
              ),
            );
          },
        ),
        
        SizedBox(height: UXThemeConfig.paddingM),
        
        // 镜子
        Container(
          width: 60,
          height: 100,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.grey.shade300,
                Colors.grey.shade100,
                Colors.grey.shade300,
              ],
            ),
            borderRadius: BorderRadius.circular(UXThemeConfig.radiusS),
            border: Border.all(
              color: Colors.grey.shade400,
              width: 2,
            ),
          ),
          child: Center(
            child: Text(
              '镜子',
              style: TextStyle(
                fontSize: UXThemeConfig.fontSizeXS,
                color: UXThemeConfig.textSecondary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMirrorFrame() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '镜像后的衣服',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: UXThemeConfig.textSecondary,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: UXThemeConfig.paddingM),
          
          // 镜像衣服轮廓（虚线框）
          Container(
            width: 120,
            height: 160,
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
              border: Border.all(
                color: UXThemeConfig.borderPrimary,
                width: 2,
                strokeAlign: BorderSide.strokeAlignInside,
              ),
            ),
            child: Center(
              child: Icon(
                Icons.help_outline,
                size: 48,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptionsArea() {
    final options = widget.puzzleData['options'] as List<Map<String, dynamic>>? ?? [];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '请选择正确的镜像衣服：',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: UXThemeConfig.textDark,
          ),
        ),
        SizedBox(height: UXThemeConfig.paddingM),
        
        Expanded(
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 0.8,
            ),
            itemCount: options.length,
            itemBuilder: (context, index) {
              final option = options[index];
              final optionId = option['id'] as String;
              final isSelected = _selectedAnswer == optionId;
              
              return _buildOptionCard(option, isSelected, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildOptionCard(Map<String, dynamic> option, bool isSelected, int index) {
    final optionId = option['id'] as String;
    
    return GestureDetector(
      onTap: _showFeedback ? null : () => _selectOption(optionId),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: isSelected 
              ? UXThemeConfig.primaryBlue.withValues(alpha: 0.1)
              : Colors.white,
          borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
          border: Border.all(
            color: isSelected 
                ? UXThemeConfig.primaryBlue
                : UXThemeConfig.borderPrimary,
            width: isSelected ? 3 : 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: isSelected ? 8 : 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 选项标签
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: isSelected 
                    ? UXThemeConfig.primaryBlue
                    : Colors.white,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  String.fromCharCode(65 + index), // A, B, C, D
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: UXThemeConfig.fontSizeM,
                  ),
                ),
              ),
            ),
            
            SizedBox(height: UXThemeConfig.paddingS),
            
            // 选项衣服
            Container(
              width: 80,
              height: 100,
              decoration: BoxDecoration(
                color: _getClothesColor(),
                borderRadius: BorderRadius.circular(UXThemeConfig.radiusS),
                border: Border.all(
                  color: UXThemeConfig.borderPrimary,
                  width: 1,
                ),
              ),
              child: Stack(
                children: [
                  _buildClothesShape(scale: 0.7),
                  _buildPattern(isOriginal: false, optionData: option, scale: 0.7),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // 提示按钮
        if (widget.onHintRequested != null)
          ElevatedButton.icon(
            onPressed: _showFeedback ? null : widget.onHintRequested,
            icon: Icon(Icons.lightbulb_outline, size: 20),
            label: const Text('提示'),
            style: ElevatedButton.styleFrom(
              backgroundColor: UXThemeConfig.accentTeal,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(
                horizontal: UXThemeConfig.paddingL,
                vertical: UXThemeConfig.paddingM,
              ),
            ),
          ),
        
        // 查看解析按钮
        if (_showFeedback)
          ElevatedButton.icon(
            onPressed: () => setState(() => _showExplanation = !_showExplanation),
            icon: Icon(
              _showExplanation ? Icons.visibility_off : Icons.visibility,
              size: 20,
            ),
            label: Text(_showExplanation ? '隐藏解析' : '查看解析'),
            style: ElevatedButton.styleFrom(
              backgroundColor: UXThemeConfig.infoBlue,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(
                horizontal: UXThemeConfig.paddingL,
                vertical: UXThemeConfig.paddingM,
              ),
            ),
          ),
        
        // 重新开始按钮
        if (widget.onRestart != null)
          ElevatedButton.icon(
            onPressed: widget.onRestart,
            icon: Icon(Icons.refresh, size: 20),
            label: const Text('重新开始'),
            style: ElevatedButton.styleFrom(
              backgroundColor: UXThemeConfig.secondaryOrange,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(
                horizontal: UXThemeConfig.paddingL,
                vertical: UXThemeConfig.paddingM,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildAnswerExplanation() {
    return Container(
      padding: EdgeInsets.all(UXThemeConfig.paddingL),
      decoration: BoxDecoration(
        color: _isCorrect 
            ? UXThemeConfig.successGreen.withValues(alpha: 0.1)
            : UXThemeConfig.errorRed.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
        border: Border.all(
          color: _isCorrect 
              ? UXThemeConfig.successGreen
              : UXThemeConfig.errorRed,
          width: 2,
        ),
      ),
      child: Column(
        children: [
          // 结果图标和标题
          Row(
            children: [
              Icon(
                _isCorrect ? Icons.check_circle : Icons.cancel,
                color: _isCorrect 
                    ? UXThemeConfig.successGreen
                    : UXThemeConfig.errorRed,
                size: 32,
              ),
              SizedBox(width: UXThemeConfig.paddingM),
              Expanded(
                child: Text(
                  _isCorrect ? '回答正确！' : '回答错误',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: _isCorrect 
                        ? UXThemeConfig.successGreen
                        : UXThemeConfig.errorRed,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          
          SizedBox(height: UXThemeConfig.paddingL),
          
          // 对比展示
          Row(
            children: [
              // 原始图案
              Expanded(
                child: Column(
                  children: [
                    Text(
                      '原始图案',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: UXThemeConfig.paddingS),
                    Container(
                      width: 100,
                      height: 120,
                      decoration: BoxDecoration(
                        color: _getClothesColor(),
                        borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
                        border: Border.all(color: UXThemeConfig.borderPrimary),
                      ),
                      child: Stack(
                        children: [
                          _buildClothesShape(scale: 0.8),
                          _buildPattern(isOriginal: true, scale: 0.8),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              
              // 箭头
              Icon(
                Icons.arrow_forward,
                size: 32,
                color: UXThemeConfig.primaryBlue,
              ),
              
              // 正确的镜像
              Expanded(
                child: Column(
                  children: [
                    Text(
                      '正确镜像',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: UXThemeConfig.successGreen,
                      ),
                    ),
                    SizedBox(height: UXThemeConfig.paddingS),
                    Container(
                      width: 100,
                      height: 120,
                      decoration: BoxDecoration(
                        color: _getClothesColor(),
                        borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
                        border: Border.all(
                          color: UXThemeConfig.successGreen,
                          width: 2,
                        ),
                      ),
                      child: Stack(
                        children: [
                          _buildClothesShape(scale: 0.8),
                          _buildPattern(
                            isOriginal: false, 
                            optionData: _getCorrectOption(),
                            scale: 0.8,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildExplanationSteps() {
    final steps = [
      {
        'title': '第一步：理解镜像原理',
        'content': '镜像会让左右位置颠倒，就像照镜子一样。',
        'icon': Icons.swap_horiz,
      },
      {
        'title': '第二步：观察图案位置',
        'content': '仔细观察原始衣服上图案的位置和形状。',
        'icon': Icons.search,
      },
      {
        'title': '第三步：想象镜像效果',
        'content': '想象图案通过镜子反射后的样子。',
        'icon': Icons.flip,
      },
      {
        'title': '第四步：选择正确答案',
        'content': '在选项中找到与镜像效果匹配的衣服。',
        'icon': Icons.check_circle,
      },
    ];

    return ListView.builder(
      itemCount: steps.length,
      itemBuilder: (context, index) {
        final step = steps[index];
        return Container(
          margin: EdgeInsets.only(bottom: UXThemeConfig.paddingM),
          padding: EdgeInsets.all(UXThemeConfig.paddingM),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
            border: Border.all(color: UXThemeConfig.borderPrimary),
          ),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: UXThemeConfig.primaryBlue,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  step['icon'] as IconData,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              SizedBox(width: UXThemeConfig.paddingM),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      step['title'] as String,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: UXThemeConfig.textDark,
                      ),
                    ),
                    SizedBox(height: UXThemeConfig.paddingXS),
                    Text(
                      step['content'] as String,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: UXThemeConfig.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 辅助方法
  Color _getClothesColor() {
    final colorName = widget.puzzleData['clothesColor'] as String? ?? 'blue';
    switch (colorName) {
      case 'red':
        return Colors.red.shade100;
      case 'blue':
        return Colors.blue.shade100;
      case 'green':
        return Colors.green.shade100;
      case 'yellow':
        return Colors.yellow.shade100;
      default:
        return Colors.blue.shade100;
    }
  }

  Widget _buildClothesShape({double scale = 1.0}) {
    return Center(
      child: Transform.scale(
        scale: scale,
        child: Container(
          width: 80,
          height: 120,
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(UXThemeConfig.radiusS),
          ),
          child: CustomPaint(
            painter: _ClothesPainter(),
          ),
        ),
      ),
    );
  }

  Widget _buildPattern({
    required bool isOriginal, 
    Map<String, dynamic>? optionData,
    double scale = 1.0,
  }) {
    final patternType = isOriginal 
        ? widget.puzzleData['originalPattern'] as String? ?? 'star'
        : optionData?['pattern'] as String? ?? 'star';
    
    final position = isOriginal 
        ? widget.puzzleData['originalPosition'] as String? ?? 'left'
        : optionData?['position'] as String? ?? 'left';

    return Center(
      child: Transform.scale(
        scale: scale,
        child: _buildPatternWidget(patternType, position),
      ),
    );
  }

  Widget _buildPatternWidget(String patternType, String position) {
    Widget pattern;
    
    switch (patternType) {
      case 'star':
        pattern = Icon(
          Icons.star,
          color: UXThemeConfig.accentTeal,
          size: 24,
        );
        break;
      case 'heart':
        pattern = Icon(
          Icons.favorite,
          color: UXThemeConfig.errorRed,
          size: 24,
        );
        break;
      case 'circle':
        pattern = Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: UXThemeConfig.secondaryOrange,
            shape: BoxShape.circle,
          ),
        );
        break;
      default:
        pattern = Icon(
          Icons.star,
          color: UXThemeConfig.accentTeal,
          size: 24,
        );
    }

    // 根据位置放置图案
    return Positioned(
      left: position == 'left' ? 20 : null,
      right: position == 'right' ? 20 : null,
      top: 40,
      child: pattern,
    );
  }

  Map<String, dynamic> _getCorrectOption() {
    final options = widget.puzzleData['options'] as List<Map<String, dynamic>>? ?? [];
    final correctAnswer = widget.puzzleData['correctAnswer'] as String? ?? '';
    
    return options.firstWhere(
      (option) => option['id'] == correctAnswer,
      orElse: () => {},
    );
  }

  void _selectOption(String optionId) {
    setState(() {
      _selectedAnswer = optionId;
    });

    // 延迟一下再显示反馈，让用户看到选择效果
    Future.delayed(const Duration(milliseconds: 300), () {
      _showAnswerFeedback(optionId);
    });
  }

  void _showAnswerFeedback(String selectedAnswer) {
    final correctAnswer = widget.puzzleData['correctAnswer'] as String? ?? '';
    final isCorrect = selectedAnswer == correctAnswer;

    setState(() {
      _isCorrect = isCorrect;
      _showFeedback = true;
    });

    // 播放反馈动画
    _feedbackAnimation.forward();

    // 通知父组件答案选择
    widget.onAnswerSelected(selectedAnswer);
  }
}

/// 衣服形状绘制器
class _ClothesPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.shade300
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    final path = Path();
    
    // 绘制简化的T恤形状
    // 上部分（肩膀）
    path.moveTo(size.width * 0.2, size.height * 0.2);
    path.lineTo(size.width * 0.8, size.height * 0.2);
    
    // 右袖
    path.lineTo(size.width * 0.9, size.height * 0.3);
    path.lineTo(size.width * 0.8, size.height * 0.4);
    
    // 右侧身体
    path.lineTo(size.width * 0.8, size.height * 0.9);
    
    // 底部
    path.lineTo(size.width * 0.2, size.height * 0.9);
    
    // 左侧身体
    path.lineTo(size.width * 0.2, size.height * 0.4);
    
    // 左袖
    path.lineTo(size.width * 0.1, size.height * 0.3);
    path.lineTo(size.width * 0.2, size.height * 0.2);

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
} 