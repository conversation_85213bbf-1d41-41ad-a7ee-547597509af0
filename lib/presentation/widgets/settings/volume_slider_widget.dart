import 'package:flutter/material.dart';
import '../../../core/theme/ux_theme_config.dart';

/// 音量滑块组件
class VolumeSliderWidget extends StatefulWidget {
  final String title;
  final IconData icon;
  final double value;
  final bool isAdjusting;
  final ValueChanged<double> onChanged;

  const VolumeSliderWidget({
    super.key,
    required this.title,
    required this.icon,
    required this.value,
    this.isAdjusting = false,
    required this.onChanged,
  });

  @override
  State<VolumeSliderWidget> createState() => _VolumeSliderWidgetState();
}

class _VolumeSliderWidgetState extends State<VolumeSliderWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void didUpdateWidget(VolumeSliderWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isAdjusting && !oldWidget.isAdjusting) {
      _animationController.forward();
    } else if (!widget.isAdjusting && oldWidget.isAdjusting) {
      _animationController.reverse();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final volumePercent = (widget.value * 100).round();

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: widget.isAdjusting
                  ? theme.colorScheme.primary.withOpacity(0.1)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
              border: widget.isAdjusting
                  ? Border.all(
                      color: theme.colorScheme.primary.withOpacity(0.3),
                      width: 2,
                    )
                  : null,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题行
                Row(
                  children: [
                    Icon(
                      widget.icon,
                      color: theme.colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      widget.title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    // 音量百分比显示
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(
                          UXThemeConfig.radiusM,
                        ),
                      ),
                      child: Text(
                        '$volumePercent%',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // 滑块
                Row(
                  children: [
                    // 静音图标
                    Icon(
                      _getVolumeIcon(widget.value),
                      color: widget.value == 0
                          ? theme.colorScheme.onSurface.withOpacity(0.6)
                          : theme.colorScheme.primary,
                      size: 16,
                    ),

                    const SizedBox(width: 8),

                    // 滑块
                    Expanded(
                      child: SliderTheme(
                        data: SliderTheme.of(context).copyWith(
                          activeTrackColor: theme.colorScheme.primary,
                          inactiveTrackColor: theme.colorScheme.primary
                              .withOpacity(0.3),
                          thumbColor: theme.colorScheme.primary,
                          overlayColor: theme.colorScheme.primary.withOpacity(0.2,),
                          thumbShape: const RoundSliderThumbShape(
                            enabledThumbRadius: 10,
                          ),
                          trackHeight: 4,
                        ),
                        child: Slider(
                          value: widget.value,
                          min: 0.0,
                          max: 1.0,
                          divisions: 20,
                          onChanged: widget.onChanged,
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // 最大音量图标
                    Icon(
                      Icons.volume_up,
                      color: theme.colorScheme.primary,
                      size: 16,
                    ),
                  ],
                ),

                // 音量级别指示器
                const SizedBox(height: 8),
                _buildVolumeIndicator(context, volumePercent),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 根据音量值获取对应的图标
  IconData _getVolumeIcon(double volume) {
    if (volume == 0) {
      return Icons.volume_off;
    } else if (volume < 0.3) {
      return Icons.volume_mute;
    } else if (volume < 0.7) {
      return Icons.volume_down;
    } else {
      return Icons.volume_up;
    }
  }

  /// 构建音量级别指示器
  Widget _buildVolumeIndicator(BuildContext context, int volumePercent) {
    final theme = Theme.of(context);
    return Row(
      children: [
        Text(
          _getVolumeDescription(volumePercent),
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
        const Spacer(),
        // 音量级别条
        Row(
          children: List.generate(5, (index) {
            final isActive = volumePercent > (index * 20);
            return Container(
              width: 8,
              height: 4,
              margin: const EdgeInsets.only(left: 2),
              decoration: BoxDecoration(
                color: isActive
                    ? theme.colorScheme.primary
                    : theme.colorScheme.primary.withOpacity(0.2),
                borderRadius: BorderRadius.circular(2),
              ),
            );
          }),
        ),
      ],
    );
  }

  /// 获取音量描述文本
  String _getVolumeDescription(int volumePercent) {
    if (volumePercent == 0) {
      return '静音';
    } else if (volumePercent < 20) {
      return '很低';
    } else if (volumePercent < 40) {
      return '较低';
    } else if (volumePercent < 60) {
      return '中等';
    } else if (volumePercent < 80) {
      return '较高';
    } else {
      return '最高';
    }
  }
}
