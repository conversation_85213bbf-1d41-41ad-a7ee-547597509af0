import 'package:flutter/material.dart';

import '../../../core/theme/ux_theme_config.dart';
import '../../../services/theme_service.dart';
import '../../../core/service_locator.dart';

/// 主题设置组件
class ThemeSettingsWidget extends StatefulWidget {
  const ThemeSettingsWidget({super.key});

  @override
  State<ThemeSettingsWidget> createState() => _ThemeSettingsWidgetState();
}

class _ThemeSettingsWidgetState extends State<ThemeSettingsWidget>
    with TickerProviderStateMixin {
  late ThemeService _themeService;
  late AnimationController _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _themeService = sl<ThemeService>();
    
    _fadeAnimation = AnimationController(
      duration: UXThemeConfig.animationNormal,
      vsync: this,
    );
    
    _fadeAnimation.forward();
  }

  @override
  void dispose() {
    _fadeAnimation.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _themeService,
      builder: (context, child) {
        return _buildContent();
      },
    );
  }

  Widget _buildContent() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Card(
        margin: EdgeInsets.all(UXThemeConfig.paddingM),
        child: Padding(
          padding: EdgeInsets.all(UXThemeConfig.paddingL),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Row(
                children: [
                  Icon(
                    Icons.palette,
                    color: UXThemeConfig.primaryBlue,
                    size: 24,
                  ),
                  SizedBox(width: UXThemeConfig.paddingS),
                  Text(
                    '主题外观',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: UXThemeConfig.textDark,
                    ),
                  ),
                ],
              ),
              
              SizedBox(height: UXThemeConfig.paddingM),
              
              // 主题模式选择
              _buildThemeModeSelector(),
              
              SizedBox(height: UXThemeConfig.paddingL),
              
              // 无障碍选项
              _buildAccessibilityOptions(),
              
              SizedBox(height: UXThemeConfig.paddingL),
              
              // 操作按钮
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建主题模式选择器
  Widget _buildThemeModeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '主题模式',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: UXThemeConfig.paddingS),
        
        Row(
          children: ThemeMode.values.map((mode) {
            final isSelected = _themeService.themeMode == mode;
            return Expanded(
              child: Padding(
                padding: EdgeInsets.only(right: UXThemeConfig.paddingS),
                child: _ThemeModeCard(
                  mode: mode,
                  isSelected: isSelected,
                  onTap: () => _changeThemeMode(mode),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// 构建无障碍选项
  Widget _buildAccessibilityOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '无障碍支持',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: UXThemeConfig.paddingS),
        
        _SettingsTile(
          icon: Icons.contrast,
          title: '高对比度模式',
          subtitle: '提高色彩对比度，便于视觉识别',
          value: _themeService.highContrastMode,
          onChanged: (value) => _toggleHighContrast(value),
        ),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _resetThemeSettings,
            icon: Icon(Icons.restore, size: 18),
            label: const Text('重置默认'),
            style: OutlinedButton.styleFrom(
              foregroundColor: UXThemeConfig.textSecondary,
              side: BorderSide(color: UXThemeConfig.borderPrimary),
            ),
          ),
        ),
        SizedBox(width: UXThemeConfig.paddingM),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _previewTheme,
            icon: Icon(Icons.preview, size: 18),
            label: const Text('预览效果'),
            style: ElevatedButton.styleFrom(
              backgroundColor: UXThemeConfig.primaryBlue,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  // 事件处理方法
  void _changeThemeMode(ThemeMode mode) {
    _themeService.setThemeMode(mode);
  }

  void _toggleHighContrast(bool value) {
    _themeService.setHighContrastMode(value);
  }

  void _resetThemeSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('重置主题设置'),
        content: const Text('确定要将主题设置重置为默认值吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _themeService.resetToDefaults();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('主题设置已重置')),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: UXThemeConfig.warningOrange,
            ),
            child: const Text('重置'),
          ),
        ],
      ),
    );
  }

  void _previewTheme() {
    Navigator.pushNamed(context, '/theme_demo');
  }
}

/// 主题模式卡片
class _ThemeModeCard extends StatelessWidget {
  final ThemeMode mode;
  final bool isSelected;
  final VoidCallback onTap;

  const _ThemeModeCard({
    required this.mode,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: UXThemeConfig.animationFast,
        padding: EdgeInsets.all(UXThemeConfig.paddingM),
        decoration: BoxDecoration(
          color: isSelected 
              ? UXThemeConfig.primaryBlue.withOpacity(0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
          border: Border.all(
            color: isSelected 
                ? UXThemeConfig.primaryBlue
                : UXThemeConfig.borderPrimary,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              _getThemeModeIcon(mode),
              color: isSelected 
                  ? UXThemeConfig.primaryBlue
                  : UXThemeConfig.textSecondary,
              size: 32,
            ),
            SizedBox(height: UXThemeConfig.paddingS),
            Text(
              _getThemeModeName(mode),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: isSelected 
                    ? UXThemeConfig.primaryBlue
                    : UXThemeConfig.textSecondary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getThemeModeIcon(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.brightness_auto;
    }
  }

  String _getThemeModeName(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return '亮色';
      case ThemeMode.dark:
        return '暗色';
      case ThemeMode.system:
        return '跟随系统';
    }
  }
}

/// 设置项瓦片
class _SettingsTile extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final bool value;
  final ValueChanged<bool> onChanged;

  const _SettingsTile({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
        border: Border.all(color: UXThemeConfig.borderPrimary),
      ),
      child: SwitchListTile(
        secondary: Icon(
          icon,
          color: UXThemeConfig.primaryBlue,
        ),
        title: Text(
          title,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: UXThemeConfig.textSecondary,
          ),
        ),
        value: value,
        onChanged: onChanged,
        activeColor: UXThemeConfig.primaryBlue,
      ),
    );
  }
} 