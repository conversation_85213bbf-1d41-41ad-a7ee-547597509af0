import 'package:flutter/material.dart';
import '../../../core/theme/ux_theme_config.dart';

/// 时间限制选择器组件
class TimeLimitSelectorWidget extends StatelessWidget {
  final int currentMinutes;
  final bool isSetting;
  final ValueChanged<int> onTimeLimitChanged;

  const TimeLimitSelectorWidget({
    super.key,
    required this.currentMinutes,
    this.isSetting = false,
    required this.onTimeLimitChanged,
  });

  static const List<Map<String, dynamic>> _timeLimitOptions = [
    {'minutes': 15, 'label': '15分钟', 'description': '短时间游戏'},
    {'minutes': 30, 'label': '30分钟', 'description': '适中时间'},
    {'minutes': 45, 'label': '45分钟', 'description': '较长时间'},
    {'minutes': 60, 'label': '1小时', 'description': '标准时间'},
    {'minutes': 90, 'label': '1.5小时', 'description': '延长时间'},
    {'minutes': 120, 'label': '2小时', 'description': '较长时间'},
    {'minutes': 180, 'label': '3小时', 'description': '很长时间'},
    {'minutes': 0, 'label': '无限制', 'description': '不限制游戏时间'},
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final currentOption = _timeLimitOptions.firstWhere(
      (option) => option['minutes'] == currentMinutes,
      orElse: () => _timeLimitOptions[3], // 默认1小时
    );

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: theme.colorScheme.primary.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(Icons.timer, color: theme.colorScheme.primary),
        ),
        title: const Text(
          '每日时间限制',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: isSetting
            ? Row(
                children: [
                  SizedBox(
                    width: 12,
                    height: 12,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        theme.colorScheme.primary,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Text('正在设置...'),
                ],
              )
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    currentOption['label'],
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    currentOption['description'],
                    style: TextStyle(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
        trailing: isSetting ? null : const Icon(Icons.chevron_right),
        onTap: isSetting ? null : () => _showTimeLimitDialog(context),
      ),
    );
  }

  /// 显示时间限制选择对话框
  void _showTimeLimitDialog(BuildContext context) {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.timer, color: theme.colorScheme.primary),
            const SizedBox(width: 8),
            const Text('设置每日时间限制'),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 说明文字
              Container(
                padding: const EdgeInsets.all(12),
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: theme.colorScheme.primary,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '设置孩子每天可以游戏的时间上限',
                        style: TextStyle(
                          color: theme.colorScheme.primary,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // 时间选项列表
              Flexible(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: _timeLimitOptions.length,
                  itemBuilder: (context, index) {
                    final option = _timeLimitOptions[index];
                    final isSelected = option['minutes'] == currentMinutes;

                    return Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: isSelected
                              ? theme.colorScheme.primary
                              : Colors.grey.withValues(alpha: 0.3),
                        ),
                        borderRadius: BorderRadius.circular(
                          UXThemeConfig.radiusM,
                        ),
                        color: isSelected
                            ? theme.colorScheme.primary.withValues(alpha: 0.1)
                            : null,
                      ),
                      child: ListTile(
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 4,
                        ),
                        leading: Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: isSelected
                                ? theme.colorScheme.primary
                                : Colors.grey.withValues(alpha: 0.3),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Icon(
                            _getTimeIcon(option['minutes']),
                            color: isSelected ? Colors.white : Colors.grey,
                            size: 16,
                          ),
                        ),
                        title: Text(
                          option['label'],
                          style: TextStyle(
                            fontWeight: isSelected
                                ? FontWeight.bold
                                : FontWeight.normal,
                            color: isSelected
                                ? theme.colorScheme.primary
                                : null,
                          ),
                        ),
                        subtitle: Text(
                          option['description'],
                          style: TextStyle(
                            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                            fontSize: 12,
                          ),
                        ),
                        trailing: isSelected
                            ? Icon(
                                Icons.check_circle,
                                color: theme.colorScheme.primary,
                              )
                            : null,
                        onTap: () {
                          Navigator.of(context).pop();
                          if (!isSelected) {
                            onTimeLimitChanged(option['minutes']);
                          }
                        },
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement clear all functionality
            },
            child: const Text('清除全部'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  /// 根据时间获取对应的图标
  IconData _getTimeIcon(int minutes) {
    if (minutes == 0) {
      return Icons.all_inclusive;
    } else if (minutes <= 30) {
      return Icons.schedule;
    } else if (minutes <= 60) {
      return Icons.access_time;
    } else if (minutes <= 120) {
      return Icons.timer_3;
    } else {
      return Icons.timer_10;
    }
  }
}
