import 'package:flutter/material.dart';
import '../../../core/theme/ux_theme_config.dart';

/// 时间段选择器组件
class TimeSlotselectorWidget extends StatefulWidget {
  final List<String> disabledTimeSlots;
  final ValueChanged<List<String>> onTimeSlotsChanged;

  const TimeSlotselectorWidget({
    super.key,
    required this.disabledTimeSlots,
    required this.onTimeSlotsChanged,
  });

  @override
  State<TimeSlotselectorWidget> createState() => _TimeSlotselectorWidgetState();
}

class _TimeSlotselectorWidgetState extends State<TimeSlotselectorWidget> {
  static const List<Map<String, String>> _timeSlots = [
    {'slot': '6:00-7:00', 'label': '早上 6-7点', 'period': '早晨'},
    {'slot': '7:00-8:00', 'label': '早上 7-8点', 'period': '早晨'},
    {'slot': '8:00-9:00', 'label': '早上 8-9点', 'period': '早晨'},
    {'slot': '9:00-10:00', 'label': '上午 9-10点', 'period': '上午'},
    {'slot': '10:00-11:00', 'label': '上午 10-11点', 'period': '上午'},
    {'slot': '11:00-12:00', 'label': '上午 11-12点', 'period': '上午'},
    {'slot': '12:00-13:00', 'label': '中午 12-1点', 'period': '中午'},
    {'slot': '13:00-14:00', 'label': '下午 1-2点', 'period': '下午'},
    {'slot': '14:00-15:00', 'label': '下午 2-3点', 'period': '下午'},
    {'slot': '15:00-16:00', 'label': '下午 3-4点', 'period': '下午'},
    {'slot': '16:00-17:00', 'label': '下午 4-5点', 'period': '下午'},
    {'slot': '17:00-18:00', 'label': '下午 5-6点', 'period': '下午'},
    {'slot': '18:00-19:00', 'label': '晚上 6-7点', 'period': '晚上'},
    {'slot': '19:00-20:00', 'label': '晚上 7-8点', 'period': '晚上'},
    {'slot': '20:00-21:00', 'label': '晚上 8-9点', 'period': '晚上'},
    {'slot': '21:00-22:00', 'label': '晚上 9-10点', 'period': '晚上'},
    {'slot': '22:00-23:00', 'label': '晚上 10-11点', 'period': '深夜'},
    {'slot': '23:00-0:00', 'label': '晚上 11-12点', 'period': '深夜'},
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: theme.colorScheme.primary.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(Icons.schedule, color: theme.colorScheme.primary),
        ),
        title: const Text(
          '禁用时间段',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.disabledTimeSlots.isEmpty
                  ? '无限制'
                  : '已禁用 ${widget.disabledTimeSlots.length} 个时间段',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 2),
            Text(
              '设置不允许游戏的时间段',
              style: TextStyle(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                fontSize: 12,
              ),
            ),
          ],
        ),
        trailing: const Icon(Icons.chevron_right),
        onTap: () => _showTimeSlotsDialog(context),
      ),
    );
  }

  void _showTimeSlotsDialog(BuildContext context) {
    final selectedSlots = List<String>.from(widget.disabledTimeSlots);
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Row(
            children: [
              Icon(Icons.schedule, color: theme.colorScheme.primary),
              const SizedBox(width: 8),
              const Text('设置禁用时间段'),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: UXThemeConfig.warning.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: UXThemeConfig.warning,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '选择不允许孩子游戏的时间段',
                          style: TextStyle(
                            color: UXThemeConfig.warning,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          setState(() {
                            final studySlots = [
                              '8:00-9:00',
                              '9:00-10:00',
                              '10:00-11:00',
                              '11:00-12:00',
                              '14:00-15:00',
                              '15:00-16:00',
                              '16:00-17:00',
                            ];
                            selectedSlots.clear();
                            selectedSlots.addAll(studySlots);
                          });
                        },
                        icon: const Icon(Icons.school, size: 16),
                        label: const Text(
                          '学习时间',
                          style: TextStyle(fontSize: 12),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          setState(() {
                            final sleepSlots = [
                              '22:00-23:00',
                              '23:00-0:00',
                              '6:00-7:00',
                            ];
                            selectedSlots.clear();
                            selectedSlots.addAll(sleepSlots);
                          });
                        },
                        icon: const Icon(Icons.bedtime, size: 16),
                        label: const Text(
                          '睡眠时间',
                          style: TextStyle(fontSize: 12),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: ListView.builder(
                    itemCount: _timeSlots.length,
                    itemBuilder: (context, index) {
                      final timeSlot = _timeSlots[index];
                      final isSelected = selectedSlots.contains(
                        timeSlot['slot'],
                      );
                      return Container(
                        margin: const EdgeInsets.only(bottom: 4),
                        child: CheckboxListTile(
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 8,
                          ),
                          dense: true,
                          value: isSelected,
                          onChanged: (value) {
                            setState(() {
                              if (value == true) {
                                selectedSlots.add(timeSlot['slot']!);
                              } else {
                                selectedSlots.remove(timeSlot['slot']);
                              }
                            });
                          },
                          title: Text(
                            timeSlot['label']!,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: isSelected
                                  ? FontWeight.w600
                                  : FontWeight.normal,
                            ),
                          ),
                          subtitle: Text(
                            timeSlot['period']!,
                            style: TextStyle(
                              fontSize: 12,
                              color: theme.colorScheme.onSurface.withValues(alpha: 
                                0.6,
                              ),
                            ),
                          ),
                          secondary: Container(
                            width: 32,
                            height: 32,
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? UXThemeConfig.warning.withValues(alpha: 0.2)
                                  : Colors.grey.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Icon(
                              _getPeriodIcon(timeSlot['period']!),
                              size: 16,
                              color: isSelected
                                  ? UXThemeConfig.warning
                                  : Colors.grey,
                            ),
                          ),
                          activeColor: UXThemeConfig.warning,
                          checkColor: Colors.white,
                        ),
                      );
                    },
                  ),
                ),
                if (selectedSlots.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.all(8),
                    margin: const EdgeInsets.only(top: 8),
                    decoration: BoxDecoration(
                      color: UXThemeConfig.warning.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(
                        UXThemeConfig.radiusM,
                      ),
                    ),
                    child: Text(
                      '已选择 ${selectedSlots.length} 个时间段',
                      style: TextStyle(
                        color: UXThemeConfig.warning,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  selectedSlots.clear();
                });
              },
              child: const Text('清除全部'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                widget.onTimeSlotsChanged(selectedSlots);
              },
              child: const Text('确定'),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getPeriodIcon(String period) {
    switch (period) {
      case '早晨':
        return Icons.wb_sunny;
      case '上午':
        return Icons.light_mode;
      case '中午':
        return Icons.wb_sunny_outlined;
      case '下午':
        return Icons.wb_cloudy;
      case '晚上':
        return Icons.nights_stay;
      case '深夜':
        return Icons.bedtime;
      default:
        return Icons.access_time;
    }
  }
}
