import 'package:flutter/material.dart';
import '../../core/theme/ux_theme_config.dart';
import '../../data/models/puzzle.dart';

/// 通用答案解析组件
///
/// 支持所有游戏类型的答案解析展示，提供统一的UI体验
class UniversalAnswerExplanationWidget extends StatefulWidget {
  /// 解析数据
  final UniversalAnswerExplanation explanation;

  /// 是否显示正确答案
  final bool isCorrect;

  /// 用户选择的答案
  final String? userAnswer;

  /// 关闭回调
  final VoidCallback? onClose;

  /// 重试回调
  final VoidCallback? onRetry;

  /// 继续下一题回调
  final VoidCallback? onNext;

  const UniversalAnswerExplanationWidget({
    super.key,
    required this.explanation,
    required this.isCorrect,
    this.userAnswer,
    this.onClose,
    this.onRetry,
    this.onNext,
  });

  @override
  State<UniversalAnswerExplanationWidget> createState() =>
      _UniversalAnswerExplanationWidgetState();
}

class _UniversalAnswerExplanationWidgetState
    extends State<UniversalAnswerExplanationWidget>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  int _currentStepIndex = 0;
  bool _showSteps = false;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0.3, 0), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
        );

    // 启动动画
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Container(
          padding: EdgeInsets.all(UXThemeConfig.paddingL),
          decoration: BoxDecoration(
            color: widget.isCorrect
                ? UXThemeConfig.successGreen.withOpacity(0.1)
                : UXThemeConfig.errorRed.withOpacity(0.1),
            borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
            border: Border.all(
              color: widget.isCorrect
                  ? UXThemeConfig.successGreen
                  : UXThemeConfig.errorRed,
              width: 2,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 结果标题
              _buildResultHeader(),

              SizedBox(height: UXThemeConfig.paddingL),

              // 核心要点
              _buildKeyPoint(),

              SizedBox(height: UXThemeConfig.paddingL),

              // 解析步骤或选项解释
              Expanded(
                child: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  transitionBuilder:
                      (Widget child, Animation<double> animation) {
                        return FadeTransition(
                          opacity: animation,
                          child: SlideTransition(
                            position: Tween<Offset>(
                              begin: const Offset(0.2, 0),
                              end: Offset.zero,
                            ).animate(animation),
                            child: child,
                          ),
                        );
                      },
                  child: _showSteps
                      ? _buildExplanationSteps()
                      : _buildOptionExplanations(),
                ),
              ),

              SizedBox(height: UXThemeConfig.paddingL),

              // 操作按钮
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建结果标题
  Widget _buildResultHeader() {
    return Row(
      children: [
        Icon(
          widget.isCorrect ? Icons.check_circle : Icons.cancel,
          color: widget.isCorrect
              ? UXThemeConfig.successGreen
              : UXThemeConfig.errorRed,
          size: 32,
        ),
        SizedBox(width: UXThemeConfig.paddingM),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.isCorrect ? '回答正确！' : '回答错误',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: widget.isCorrect
                      ? UXThemeConfig.successGreen
                      : UXThemeConfig.errorRed,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (widget.userAnswer != null) ...[
                SizedBox(height: UXThemeConfig.paddingXS),
                Text(
                  '你的答案: ${widget.userAnswer}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: UXThemeConfig.textSecondary,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  /// 构建核心要点
  Widget _buildKeyPoint() {
    return Container(
      padding: EdgeInsets.all(UXThemeConfig.paddingM),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            UXThemeConfig.primaryBlue.withOpacity(0.1),
            UXThemeConfig.accentBlue.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
        border: Border.all(
          color: UXThemeConfig.primaryBlue.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: UXThemeConfig.paddingS,
                  vertical: UXThemeConfig.paddingXS,
                ),
                decoration: BoxDecoration(
                  color: UXThemeConfig.primaryBlue,
                  borderRadius: BorderRadius.circular(UXThemeConfig.radiusS),
                ),
                child: Text(
                  'Key Point',
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: UXThemeConfig.paddingS),
          Text(
            widget.explanation.keyPoint,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: UXThemeConfig.textDark,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建解析步骤
  Widget _buildExplanationSteps() {
    return Column(
      key: const ValueKey('steps'),
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 步骤标题和进度指示器
        Row(
          children: [
            Text(
              '解析步骤',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
            const Spacer(),
            _buildStepIndicator(),
          ],
        ),

        SizedBox(height: UXThemeConfig.paddingM),

        // 当前步骤内容
        Expanded(child: _buildCurrentStep()),

        // 步骤导航
        _buildStepNavigation(),
      ],
    );
  }

  /// 构建选项解释
  Widget _buildOptionExplanations() {
    return Column(
      key: const ValueKey('options'),
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '选项分析',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),

        SizedBox(height: UXThemeConfig.paddingM),

        Expanded(
          child: ListView.separated(
            itemCount: widget.explanation.optionExplanations.length,
            separatorBuilder: (context, index) =>
                SizedBox(height: UXThemeConfig.paddingM),
            itemBuilder: (context, index) {
              final option = widget.explanation.optionExplanations[index];
              return _buildOptionCard(option);
            },
          ),
        ),
      ],
    );
  }

  /// 构建步骤指示器
  Widget _buildStepIndicator() {
    return Row(
      children: List.generate(
        widget.explanation.steps.length,
        (index) => Container(
          margin: EdgeInsets.only(
            left: index > 0 ? UXThemeConfig.paddingXS : 0,
          ),
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: index == _currentStepIndex
                ? UXThemeConfig.primaryBlue
                : UXThemeConfig.borderSecondary,
            shape: BoxShape.circle,
          ),
        ),
      ),
    );
  }

  /// 构建当前步骤
  Widget _buildCurrentStep() {
    if (_currentStepIndex >= widget.explanation.steps.length) {
      return const SizedBox.shrink();
    }

    final step = widget.explanation.steps[_currentStepIndex];

    return Container(
      padding: EdgeInsets.all(UXThemeConfig.paddingM),
      decoration: BoxDecoration(
        color: UXThemeConfig.backgroundSecondary,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
        border: Border.all(color: UXThemeConfig.borderPrimary),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 步骤标题
          Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: UXThemeConfig.primaryBlue,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    '${step.stepNumber}',
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              SizedBox(width: UXThemeConfig.paddingS),
              Expanded(
                child: Text(
                  step.title,
                  style: Theme.of(
                    context,
                  ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
                ),
              ),
            ],
          ),

          SizedBox(height: UXThemeConfig.paddingS),

          // 步骤描述
          Text(step.description, style: Theme.of(context).textTheme.bodyMedium),

          // 提示信息
          if (step.tip != null) ...[
            SizedBox(height: UXThemeConfig.paddingS),
            Container(
              padding: EdgeInsets.all(UXThemeConfig.paddingS),
              decoration: BoxDecoration(
                color: UXThemeConfig.warning.withOpacity(0.1),
                borderRadius: BorderRadius.circular(UXThemeConfig.radiusS),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.lightbulb_outline,
                    color: UXThemeConfig.warning,
                    size: 16,
                  ),
                  SizedBox(width: UXThemeConfig.paddingXS),
                  Expanded(
                    child: Text(
                      step.tip!,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: UXThemeConfig.warning,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建步骤导航
  Widget _buildStepNavigation() {
    return Row(
      children: [
        // 上一步按钮
        if (_currentStepIndex > 0)
          TextButton.icon(
            onPressed: () {
              setState(() {
                _currentStepIndex--;
              });
            },
            icon: const Icon(Icons.arrow_back, size: 16),
            label: const Text('上一步'),
            style: TextButton.styleFrom(
              foregroundColor: UXThemeConfig.textSecondary,
            ),
          ),

        const Spacer(),

        // 下一步按钮
        if (_currentStepIndex < widget.explanation.steps.length - 1)
          TextButton.icon(
            onPressed: () {
              setState(() {
                _currentStepIndex++;
              });
            },
            label: const Text('下一步'),
            icon: const Icon(Icons.arrow_forward, size: 16),
            style: TextButton.styleFrom(
              foregroundColor: UXThemeConfig.primaryBlue,
            ),
          ),
      ],
    );
  }

  /// 构建选项卡片
  Widget _buildOptionCard(OptionExplanation option) {
    final isCorrect = option.isCorrect;
    final isUserChoice = widget.userAnswer == option.optionId;

    return Container(
      padding: EdgeInsets.all(UXThemeConfig.paddingM),
      decoration: BoxDecoration(
        color: isCorrect
            ? UXThemeConfig.successGreen.withOpacity(0.1)
            : isUserChoice
            ? UXThemeConfig.errorRed.withOpacity(0.1)
            : UXThemeConfig.backgroundSecondary,
        borderRadius: BorderRadius.circular(UXThemeConfig.radiusM),
        border: Border.all(
          color: isCorrect
              ? UXThemeConfig.successGreen
              : isUserChoice
              ? UXThemeConfig.errorRed
              : UXThemeConfig.borderPrimary,
          width: isCorrect || isUserChoice ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 选项标题
          Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: isCorrect
                      ? UXThemeConfig.successGreen
                      : isUserChoice
                      ? UXThemeConfig.errorRed
                      : UXThemeConfig.textSecondary,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    option.optionId,
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              SizedBox(width: UXThemeConfig.paddingS),
              Expanded(
                child: Row(
                  children: [
                    if (isCorrect) ...[
                      Icon(
                        Icons.check_circle,
                        color: UXThemeConfig.successGreen,
                        size: 16,
                      ),
                      SizedBox(width: UXThemeConfig.paddingXS),
                      Text(
                        '正确答案',
                        style: Theme.of(context).textTheme.labelMedium
                            ?.copyWith(
                              color: UXThemeConfig.successGreen,
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                    ] else if (isUserChoice) ...[
                      Icon(
                        Icons.cancel,
                        color: UXThemeConfig.errorRed,
                        size: 16,
                      ),
                      SizedBox(width: UXThemeConfig.paddingXS),
                      Text(
                        '你的选择',
                        style: Theme.of(context).textTheme.labelMedium
                            ?.copyWith(
                              color: UXThemeConfig.errorRed,
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: UXThemeConfig.paddingS),

          // 选项解释
          Text(
            option.explanation,
            style: Theme.of(context).textTheme.bodyMedium,
          ),

          // 图片对比（如果有）
          if (option.imagePath != null) ...[
            SizedBox(height: UXThemeConfig.paddingS),
            Container(
              padding: EdgeInsets.all(UXThemeConfig.paddingS),
              decoration: BoxDecoration(
                color: UXThemeConfig.backgroundPrimary,
                borderRadius: BorderRadius.circular(UXThemeConfig.radiusS),
                border: Border.all(color: UXThemeConfig.borderSecondary),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.image,
                    color: UXThemeConfig.textSecondary,
                    size: 16,
                  ),
                  SizedBox(width: UXThemeConfig.paddingXS),
                  Text(
                    '查看图片对比',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: UXThemeConfig.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Column(
      children: [
        // 切换视图按钮
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () {
                  setState(() {
                    _showSteps = !_showSteps;
                    _currentStepIndex = 0; // 重置步骤索引
                  });
                },
                icon: Icon(_showSteps ? Icons.list : Icons.timeline, size: 16),
                label: Text(_showSteps ? '查看选项分析' : '查看解析步骤'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: UXThemeConfig.primaryBlue,
                  side: BorderSide(color: UXThemeConfig.primaryBlue),
                ),
              ),
            ),
          ],
        ),

        SizedBox(height: UXThemeConfig.paddingM),

        // 主要操作按钮
        Row(
          children: [
            // 关闭按钮
            if (widget.onClose != null)
              Expanded(
                child: OutlinedButton(
                  onPressed: widget.onClose,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: UXThemeConfig.textSecondary,
                    side: BorderSide(color: UXThemeConfig.borderPrimary),
                  ),
                  child: const Text('关闭'),
                ),
              ),

            if (widget.onClose != null &&
                (widget.onRetry != null || widget.onNext != null))
              SizedBox(width: UXThemeConfig.paddingM),

            // 重试或继续按钮
            if (widget.onRetry != null && !widget.isCorrect)
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: widget.onRetry,
                  icon: const Icon(Icons.refresh, size: 16),
                  label: const Text('重试'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: UXThemeConfig.warning,
                    foregroundColor: Colors.white,
                  ),
                ),
              )
            else if (widget.onNext != null && widget.isCorrect)
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: widget.onNext,
                  icon: const Icon(Icons.arrow_forward, size: 16),
                  label: const Text('下一题'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: UXThemeConfig.successGreen,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
          ],
        ),
      ],
    );
  }
}
