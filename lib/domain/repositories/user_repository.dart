import '../entities/user_profile_entity.dart';
import '../../core/utils/result.dart';

/// 用户仓库抽象接口
/// 定义用户数据操作的契约，不依赖具体实现
/// 使用Result`&lt;T&gt;`模式进行统一的错误处理
abstract class UserRepository {
  /// 获取当前用户
  /// 
  /// 返回 Result`&lt;UserProfileEntity?&gt;` 当前用户信息，如果没有当前用户则为null
  Future<Result<UserProfileEntity?>> getCurrentUser();

  /// 根据ID获取用户
  /// 
  /// [userId] 用户ID
  /// 返回 Result`&lt;UserProfileEntity?&gt;` 用户信息，如果用户不存在则为null
  Future<Result<UserProfileEntity?>> getUserById(String userId);

  /// 获取所有用户
  /// 
  /// 返回 Result`&lt;List<UserProfileEntity>&gt;` 所有用户列表
  Future<Result<List<UserProfileEntity>>> getAllUsers();

  /// 创建新用户
  /// 
  /// [nickname] 昵称
  /// [avatarId] 头像ID
  /// 返回 Result`&lt;UserProfileEntity&gt;` 创建的用户信息
  Future<Result<UserProfileEntity>> createUser({
    required String nickname,
    required String avatarId,
  });

  /// 更新用户信息
  /// 
  /// [user] 要更新的用户信息
  /// 返回 Result`&lt;void&gt;` 更新结果
  Future<Result<void>> updateUser(UserProfileEntity user);

  /// 删除用户
  /// 
  /// [userId] 要删除的用户ID
  /// 返回 Result`&lt;void&gt;` 删除结果
  Future<Result<void>> deleteUser(String userId);

  /// 设置当前用户
  /// 
  /// [userId] 要设置为当前用户的ID
  /// 返回 Result`&lt;void&gt;` 设置结果
  Future<Result<void>> setCurrentUser(String userId);

  /// 检查昵称是否已存在
  /// 
  /// [nickname] 要检查的昵称
  /// 返回 Result`&lt;bool&gt;` 是否存在
  Future<Result<bool>> isNicknameExists(String nickname);

  /// 更新用户关卡进度
  /// 
  /// [userId] 用户ID
  /// [levelId] 关卡ID
  /// [score] 得分
  /// [timeSeconds] 用时（秒）
  /// [hintsUsed] 使用的提示次数
  /// 返回 Result`&lt;void&gt;` 更新结果
  Future<Result<void>> updateLevelProgress({
    required String userId,
    required String levelId,
    required int score,
    required int timeSeconds,
    required int hintsUsed,
  });

  /// 解锁用户成就
  /// 
  /// [userId] 用户ID
  /// [achievementId] 成就ID
  /// 返回 Result`&lt;void&gt;` 解锁结果
  Future<Result<void>> unlockAchievement({
    required String userId,
    required String achievementId,
  });

  /// 更新用户设置
  /// 
  /// [userId] 用户ID
  /// [settings] 设置信息
  /// 返回 Result`&lt;void&gt;` 更新结果
  Future<Result<void>> updateUserSettings({
    required String userId,
    required UserSettingsEntity settings,
  });

  /// 增加游戏时间
  /// 
  /// [userId] 用户ID
  /// [minutes] 增加的分钟数
  /// 返回 Result`&lt;void&gt;` 更新结果
  Future<Result<void>> addPlayTime({
    required String userId,
    required int minutes,
  });

  /// 获取用户统计信息
  /// 
  /// [userId] 用户ID
  /// 返回 Result`&lt;Map<String, dynamic>&gt;` 用户统计信息
  Future<Result<Map<String, dynamic>>> getUserStats(String userId);

  /// 重置用户进度
  /// 
  /// [userId] 用户ID
  /// 返回 Result`&lt;void&gt;` 重置结果
  Future<Result<void>> resetUserProgress(String userId);
}

/// 用户查询仓库接口
/// 
/// 按照接口隔离原则，将查询操作单独分离
abstract class UserQueryRepository {
  /// 获取当前用户
  Future<Result<UserProfileEntity?>> getCurrentUser();

  /// 根据ID获取用户
  Future<Result<UserProfileEntity?>> getUserById(String userId);

  /// 获取所有用户
  Future<Result<List<UserProfileEntity>>> getAllUsers();

  /// 检查昵称是否已存在
  Future<Result<bool>> isNicknameExists(String nickname);

  /// 根据昵称查找用户
  Future<Result<UserProfileEntity?>> getUserByNickname(String nickname);

  /// 获取用户统计信息
  Future<Result<Map<String, dynamic>>> getUserStats(String userId);

  /// 获取用户数量
  Future<Result<int>> getUserCount();

  /// 分页获取用户列表
  Future<Result<List<UserProfileEntity>>> getUsersPaginated({
    int page = 1,
    int pageSize = 10,
  });

  /// 搜索用户
  Future<Result<List<UserProfileEntity>>> searchUsers({
    String? nickname,
    String? avatarId,
    int? minLevel,
    int? maxLevel,
  });
}

/// 用户命令仓库接口
/// 
/// 按照接口隔离原则，将命令操作单独分离
abstract class UserCommandRepository {
  /// 创建新用户
  Future<Result<UserProfileEntity>> createUser({
    required String nickname,
    required String avatarId,
  });

  /// 更新用户信息
  Future<Result<UserProfileEntity>> updateUser(UserProfileEntity user);

  /// 删除用户
  Future<Result<void>> deleteUser(String userId);

  /// 设置当前用户
  Future<Result<void>> setCurrentUser(String userId);

  /// 更新用户关卡进度
  Future<Result<void>> updateLevelProgress({
    required String userId,
    required String levelId,
    required int score,
    required int timeSeconds,
    required int hintsUsed,
  });

  /// 解锁用户成就
  Future<Result<void>> unlockAchievement({
    required String userId,
    required String achievementId,
  });

  /// 更新用户设置
  Future<Result<void>> updateUserSettings({
    required String userId,
    required UserSettingsEntity settings,
  });

  /// 增加游戏时间
  Future<Result<void>> addPlayTime({
    required String userId,
    required int minutes,
  });

  /// 重置用户进度
  Future<Result<void>> resetUserProgress(String userId);

  /// 批量操作接口
  
  /// 批量创建用户
  Future<Result<List<UserProfileEntity>>> createUsers(
    List<({String nickname, String avatarId})> users,
  );

  /// 批量更新用户
  Future<Result<void>> updateUsers(List<UserProfileEntity> users);

  /// 批量删除用户
  Future<Result<void>> deleteUsers(List<String> userIds);

  /// 批量解锁成就
  Future<Result<void>> unlockAchievements(
    List<({String userId, String achievementId})> achievements,
  );
} 