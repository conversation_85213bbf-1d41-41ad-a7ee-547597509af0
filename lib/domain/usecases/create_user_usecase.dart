import 'package:logger/logger.dart';

import '../entities/user_profile_entity.dart';
import '../repositories/user_repository.dart';
import '../../core/exceptions/app_exceptions.dart';
import '../../core/utils/result.dart';
import '../../core/utils/validation.dart';
import 'base_usecase.dart';

/// 创建用户用例参数
class CreateUserParams extends UseCaseParams {
  final String nickname;
  final String avatarId;

  const CreateUserParams({
    required this.nickname,
    required this.avatarId,
  });

  @override
  List<Object> get props => [nickname, avatarId];

  /// 转换为Map用于验证
  Map<String, dynamic> toMap() {
    return {
      'nickname': nickname,
      'avatarId': avatarId,
    };
  }

  @override
  ValidationResult validate() {
    // 使用批量验证器进行完整的参数验证
    final validator = BatchValidator()
        .validate('nickname', nickname, Validators.nickname())
        .validate('avatarId', avatarId, Validators.avatarId());

    return validator.toResult();
  }
}

/// 创建用户用例
/// 
/// 负责处理用户创建的业务逻辑，包括：
/// - 完整的输入参数验证
/// - 昵称唯一性检查
/// - 用户数量限制检查
/// - 用户创建和保存
/// - 统一的错误处理和结果返回
class CreateUserUseCase implements UseCase<Result<UserProfileEntity>, CreateUserParams> {
  final UserRepository _userRepository;
  final Logger _logger = Logger();

  CreateUserUseCase(this._userRepository);

  @override
  Future<Result<UserProfileEntity>> call(CreateUserParams params) async {
    _logger.i('Creating user with nickname: ${params.nickname}, avatar: ${params.avatarId}');

    return Result.fromAsync(() async {
      // 1. 输入参数验证
      final validationResult = params.validate();
      if (validationResult.isFailure) {
        _logger.w('User creation failed: parameter validation error');
        throw validationResult.exception ?? UserException(
          errorCode: 'VALIDATION_ERROR',
          message: 'Parameter validation failed',
        );
      }

      // 2. 业务逻辑验证
      await _validateBusinessRules(params);

      // 3. 创建用户实体
      final userEntity = UserProfileEntity.create(
        nickname: params.nickname.trim(),
        avatarId: params.avatarId,
      );

      // 4. 保存用户
      final savedUserResult = await _userRepository.createUser(
        nickname: userEntity.nickname,
        avatarId: userEntity.avatarId,
      );

      final savedUser = savedUserResult.getOrThrow();
      _logger.i('User created successfully: ${savedUser.id}');
      return savedUser;
    }).onError((exception, stackTrace) {
      _logger.e('Failed to create user: ${exception.toString()}', 
                error: exception, stackTrace: stackTrace);
      throw (exception ?? Exception("Unknown error"));
    });
  }

  /// 验证业务规则
  Future<void> _validateBusinessRules(CreateUserParams params) async {
    // 获取所有现有用户
    final allUsersResult = await _userRepository.getAllUsers();
    final allUsers = allUsersResult.getOrThrow();

    // 验证用户数量限制
    _validateUserCountLimit(allUsers.length);

    // 验证昵称唯一性
    _validateNicknameUniqueness(params.nickname.trim(), allUsers);
  }

  /// 验证用户数量限制
  void _validateUserCountLimit(int currentUserCount) {
    const maxUsers = 4;
    
    if (currentUserCount >= maxUsers) {
      throw UserLimitExceededException(
        currentCount: currentUserCount,
        maxCount: maxUsers,
      );
    }
  }

  /// 验证昵称唯一性
  void _validateNicknameUniqueness(
    String nickname, 
    List<UserProfileEntity> existingUsers,
  ) {
    final existingUser = existingUsers
        .where((user) => user.nickname.toLowerCase() == nickname.toLowerCase())
        .firstOrNull;

    if (existingUser != null) {
      throw NicknameAlreadyTakenException(nickname: nickname);
    }
  }
}

/// 创建用户用例的扩展验证器
class CreateUserValidator {
  CreateUserValidator._();

  /// 完整的创建用户参数验证器
  static Validator<CreateUserParams> complete() {
    return BaseValidator<CreateUserParams>(
      (params) {
        // 基础参数验证
        final basicValidation = params.validate();
        if (basicValidation.isFailure) return false;

        // 额外的业务规则验证
        return _validateBusinessConstraints(params);
      },
      () => InvalidParameterFormatException(
        parameterName: 'createUserParams',
        expectedFormat: 'valid user creation parameters',
      ),
      'Complete create user parameters validation',
    );
  }

  /// 验证业务约束
  static bool _validateBusinessConstraints(CreateUserParams params) {
    // 昵称不能是保留词
    final reservedNicknames = [
      'admin', 'system', 'guest', 'user', 'test',
      '管理员', '系统', '访客', '用户', '测试',
    ];
    
    if (reservedNicknames.contains(params.nickname.toLowerCase())) {
      return false;
    }

    // 头像ID必须在有效范围内
    final avatarNumber = int.tryParse(params.avatarId.replaceAll('avatar_', ''));
    if (avatarNumber == null || avatarNumber < 1 || avatarNumber > 12) {
      return false;
    }

    return true;
  }

  /// 昵称内容验证器（检查敏感词等）
  static Validator<String> nicknameContent() {
    return BaseValidator<String>(
      (nickname) {
        // 检查是否包含敏感词（简化示例）
        final sensitiveWords = ['fuck', 'shit', '傻逼', '白痴'];
        final lowerNickname = nickname.toLowerCase();
        
        for (final word in sensitiveWords) {
          if (lowerNickname.contains(word)) {
            return false;
          }
        }
        
        return true;
      },
      () => InvalidParameterFormatException(
        parameterName: 'nickname',
        expectedFormat: '不包含敏感词的昵称',
      ),
      'Nickname content validation',
    );
  }

  /// 头像可用性验证器
  static Validator<String> avatarAvailability() {
    return BaseValidator<String>(
      (avatarId) {
        // 检查头像是否在可用列表中
        final availableAvatars = List.generate(12, (i) => 'avatar_${i + 1}');
        return availableAvatars.contains(avatarId);
      },
      () => InvalidParameterFormatException(
        parameterName: 'avatarId',
        expectedFormat: 'avatar_1 到 avatar_12 之间的有效头像ID',
      ),
      'Avatar availability validation',
    );
  }
}

/// 创建用户用例的高级验证版本
class ValidatedCreateUserUseCase extends CreateUserUseCase {
  ValidatedCreateUserUseCase(super.userRepository);

  @override
  Future<Result<UserProfileEntity>> call(CreateUserParams params) async {
    _logger.i('Creating user with enhanced validation: ${params.nickname}');

    return Result.fromAsync(() async {
      // 1. 完整的参数验证（包括业务约束）
      final completeValidation = CreateUserValidator.complete().validate(params);
      if (completeValidation.isFailure) {
        _logger.w('Enhanced validation failed');
        throw completeValidation.exception ?? UserException(
          errorCode: 'VALIDATION_ERROR', 
          message: 'Complete validation failed',
        );
      }

      // 2. 额外的内容验证
      final contentValidation = BatchValidator()
          .validate('nickname_content', params.nickname, CreateUserValidator.nicknameContent())
          .validate('avatar_availability', params.avatarId, CreateUserValidator.avatarAvailability());

      if (!contentValidation.isValid) {
        _logger.w('Content validation failed');
        throw contentValidation.firstError ?? UserException(
          errorCode: 'CONTENT_VALIDATION_ERROR',
          message: 'Content validation failed',
        );
      }

      // 3. 调用父类方法执行创建逻辑
      final result = await super.call(params);
      final user = result.getOrThrow();
      _logger.i('User created with enhanced validation: ${user.id}');
      return user;
    }).onError((exception, stackTrace) {
      _logger.e('Enhanced user creation failed: ${exception.toString()}');
      throw (exception ?? Exception("Unknown error"));
    });
  }
} 