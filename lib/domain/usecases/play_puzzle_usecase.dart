import 'package:logger/logger.dart';

import '../../core/constants/app_constants.dart';
import '../../core/exceptions/app_exceptions.dart';
import '../../core/utils/result.dart';
import '../entities/puzzle_entity.dart';
import '../entities/user_profile_entity.dart';
import '../repositories/puzzle_repository.dart';
import '../repositories/user_repository.dart';
import 'base_usecase.dart';

/// 开始游戏参数
class PlayPuzzleParams extends UseCaseParams {
  final String userId;
  final String levelId;
  final Map<String, dynamic>? savedState;

  const PlayPuzzleParams({
    required this.userId,
    required this.levelId,
    this.savedState,
  });

  @override
  List<Object?> get props => [userId, levelId, savedState];

  @override
  Result<void> validate() {
    if (userId.isEmpty) {
      return Result.failure(
        UserException(errorCode: 'INVALID_USER_ID', message: '用户ID不能为空'),
      );
    }
    if (levelId.isEmpty) {
      return Result.failure(
        PuzzleException(errorCode: 'INVALID_LEVEL_ID', message: '关卡ID不能为空'),
      );
    }
    return Result.success(null);
  }
}

/// 游戏开始结果
class PuzzleGameResult {
  final PuzzleEntity puzzle;
  final UserProfileEntity user;
  final Map<String, dynamic> savedState;
  final bool isResume;
  final bool canResume;

  const PuzzleGameResult({
    required this.puzzle,
    required this.user,
    required this.savedState,
    required this.isResume,
    required this.canResume,
  });
}

/// 开始游戏用例
class PlayPuzzleUseCase
    implements UseCase<Result<PuzzleGameResult>, PlayPuzzleParams> {
  final PuzzleRepository _puzzleRepository;
  final UserRepository _userRepository;
  final Logger _logger = Logger();

  PlayPuzzleUseCase(this._puzzleRepository, this._userRepository);

  @override
  Future<Result<PuzzleGameResult>> call(PlayPuzzleParams params) async {
    try {
      _logger.i(
        'Starting puzzle game for user: ${params.userId}, level: ${params.levelId}',
      );

      // 验证参数
      final validation = params.validate();
      if (validation.isFailure) {
        return Result.failure(validation.exception!);
      }

      // 获取用户信息
      final userResult = await _userRepository.getUserById(params.userId);
      final user = userResult.getOrThrow();
      if (user == null) {
        return Result.failure(
          UserException(errorCode: 'USER_NOT_FOUND', message: '用户不存在'),
        );
      }

      // 获取谜题信息
      final puzzleResult = await _puzzleRepository.getPuzzleById(
        params.levelId,
      );
      final puzzle = puzzleResult.getOrThrow();
      if (puzzle == null) {
        return Result.failure(
          PuzzleException(errorCode: 'PUZZLE_NOT_FOUND', message: '谜题不存在'),
        );
      }

      // 检查游戏权限
      final permissionCheck = await _checkGamePermission(user, puzzle);
      if (permissionCheck.isFailure) {
        return Result.failure(permissionCheck.exception!);
      }

      // 准备游戏状态
      final gameState = await _prepareGameState(puzzle, params.savedState);
      final isResume = params.savedState != null;

      // 记录游戏开始
      await _recordGameStart(user, puzzle, isResume);

      final result = PuzzleGameResult(
        puzzle: puzzle,
        user: user,
        savedState: gameState,
        isResume: isResume,
        canResume: false, // Assuming canResume is false by default
      );

      _logger.i(
        'Puzzle game started successfully for level: ${params.levelId}',
      );
      return Result.success(result);
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to start puzzle game',
        error: e,
        stackTrace: stackTrace,
      );
      return Result.failure(
        PuzzleException(
          errorCode: 'START_GAME_ERROR',
          message: '开始游戏失败：${e.toString()}',
        ),
      );
    }
  }

  /// 检查游戏权限
  Future<Result<void>> _checkGamePermission(
    UserProfileEntity user,
    PuzzleEntity puzzle,
  ) async {
    // 检查用户是否有权限玩这个谜题
    // 这里可以添加更复杂的权限逻辑，比如检查前置关卡是否完成

    // 简单示例：检查难度是否适合用户级别
    final userLevel = _calculateUserLevel(user);
    if (puzzle.difficulty.index > userLevel + 1) {
      return Result.failure(
        UserException(errorCode: 'INSUFFICIENT_LEVEL', message: '您的等级不足以挑战此谜题'),
      );
    }

    return Result.success(null);
  }

  /// 准备游戏状态
  Future<Map<String, dynamic>> _prepareGameState(
    PuzzleEntity puzzle,
    Map<String, dynamic>? savedState,
  ) async {
    if (savedState != null) {
      // 恢复保存的游戏状态
      return Map<String, dynamic>.from(savedState);
    }

    // 创建新的游戏状态
    return {
      'puzzle_id': puzzle.levelId,
      'start_time': DateTime.now().toIso8601String(),
      'attempts': 0,
      'hints_used': 0,
      'current_answer': null,
      'game_data': _initializeGameData(puzzle),
    };
  }

  /// 初始化游戏数据
  Map<String, dynamic> _initializeGameData(PuzzleEntity puzzle) {
    switch (puzzle.puzzleType) {
      case PuzzleType.graphicPattern3x3:
        return {
          'pattern_type': 'visual',
          'current_selection': null,
          'completed_patterns': [],
        };
      case PuzzleType.spatialVisualization:
        return {
          'spatial_type': '3d_rotation',
          'current_rotation': 0,
          'selected_view': null,
        };
      case PuzzleType.numericLogic:
        return {
          'numeric_type': 'sequence',
          'current_sequence': [],
          'user_input': null,
        };
      case PuzzleType.introToCoding:
        return {
          'coding_type': 'algorithm',
          'current_code': '',
          'test_results': [],
        };
      case PuzzleType.mirrorSymmetry:
        return {
          'symmetry_type': 'mirror',
          'selected_cells': [],
          'symmetry_axis': null,
        };
    }
  }

  /// 记录游戏开始
  Future<void> _recordGameStart(
    UserProfileEntity user,
    PuzzleEntity puzzle,
    bool isResume,
  ) async {
    // 更新用户的最后游戏时间
    final updatedUser = user.copyWith(lastPlayedAt: DateTime.now());
    await _userRepository.updateUser(updatedUser);

    _logger.d(
      'Game start recorded for user: ${user.id}, puzzle: ${puzzle.levelId}, resume: $isResume',
    );
  }

  /// 计算用户等级
  int _calculateUserLevel(UserProfileEntity user) {
    // 基于用户的技能点数计算等级
    final totalSkillPoints = user.skillPoints.values.fold(
      0,
      (sum, points) => sum + points,
    );
    return (totalSkillPoints / 100).floor(); // 每100技能点升一级
  }
}
