// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_profile.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserProfileAdapter extends TypeAdapter<UserProfile> {
  @override
  final int typeId = 0;

  @override
  UserProfile read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserProfile(
      id: fields[0] as String,
      nickname: fields[1] as String,
      avatarId: fields[2] as String,
      createdAt: fields[3] as DateTime,
      lastPlayedAt: fields[4] as DateTime,
      levelProgress: (fields[5] as Map).cast<String, LevelProgress>(),
      unlockedAchievements: (fields[6] as List).cast<String>(),
      totalPlayTimeMinutes: fields[7] as int,
      skillPoints: (fields[8] as Map).cast<String, int>(),
      totalPoints: fields[9] as int,
      totalScore: fields[10] as int,
      settings: fields[11] as UserSettings,
    );
  }

  @override
  void write(BinaryWriter writer, UserProfile obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.nickname)
      ..writeByte(2)
      ..write(obj.avatarId)
      ..writeByte(3)
      ..write(obj.createdAt)
      ..writeByte(4)
      ..write(obj.lastPlayedAt)
      ..writeByte(5)
      ..write(obj.levelProgress)
      ..writeByte(6)
      ..write(obj.unlockedAchievements)
      ..writeByte(7)
      ..write(obj.totalPlayTimeMinutes)
      ..writeByte(8)
      ..write(obj.skillPoints)
      ..writeByte(9)
      ..write(obj.totalPoints)
      ..writeByte(10)
      ..write(obj.totalScore)
      ..writeByte(11)
      ..write(obj.settings);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserProfileAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LevelProgressAdapter extends TypeAdapter<LevelProgress> {
  @override
  final int typeId = 1;

  @override
  LevelProgress read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LevelProgress(
      levelId: fields[0] as String,
      bestScore: fields[1] as int,
      attempts: fields[2] as int,
      completed: fields[3] as bool,
      firstCompletedAt: fields[4] as DateTime?,
      lastAttemptAt: fields[5] as DateTime?,
      bestTimeSeconds: fields[6] as int,
      hintsUsed: fields[7] as int,
      starRating: fields[8] as int,
    );
  }

  @override
  void write(BinaryWriter writer, LevelProgress obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.levelId)
      ..writeByte(1)
      ..write(obj.bestScore)
      ..writeByte(2)
      ..write(obj.attempts)
      ..writeByte(3)
      ..write(obj.completed)
      ..writeByte(4)
      ..write(obj.firstCompletedAt)
      ..writeByte(5)
      ..write(obj.lastAttemptAt)
      ..writeByte(6)
      ..write(obj.bestTimeSeconds)
      ..writeByte(7)
      ..write(obj.hintsUsed)
      ..writeByte(8)
      ..write(obj.starRating);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LevelProgressAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class UserSettingsAdapter extends TypeAdapter<UserSettings> {
  @override
  final int typeId = 2;

  @override
  UserSettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserSettings(
      musicVolume: fields[0] as double,
      sfxVolume: fields[1] as double,
      enableVibration: fields[2] as bool,
      enableAnimations: fields[3] as bool,
      language: fields[4] as String,
      dailyTimeLimitMinutes: fields[5] as int,
      disabledTimeSlots: (fields[6] as List).cast<String>(),
    );
  }

  @override
  void write(BinaryWriter writer, UserSettings obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.musicVolume)
      ..writeByte(1)
      ..write(obj.sfxVolume)
      ..writeByte(2)
      ..write(obj.enableVibration)
      ..writeByte(3)
      ..write(obj.enableAnimations)
      ..writeByte(4)
      ..write(obj.language)
      ..writeByte(5)
      ..write(obj.dailyTimeLimitMinutes)
      ..writeByte(6)
      ..write(obj.disabledTimeSlots);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserSettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserProfile _$UserProfileFromJson(Map<String, dynamic> json) => UserProfile(
      id: json['id'] as String,
      nickname: json['nickname'] as String,
      avatarId: json['avatarId'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastPlayedAt: DateTime.parse(json['lastPlayedAt'] as String),
      levelProgress: (json['levelProgress'] as Map<String, dynamic>).map(
        (k, e) =>
            MapEntry(k, LevelProgress.fromJson(e as Map<String, dynamic>)),
      ),
      unlockedAchievements: (json['unlockedAchievements'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      totalPlayTimeMinutes: (json['totalPlayTimeMinutes'] as num).toInt(),
      skillPoints: Map<String, int>.from(json['skillPoints'] as Map),
      totalPoints: (json['totalPoints'] as num).toInt(),
      totalScore: (json['totalScore'] as num).toInt(),
      settings: UserSettings.fromJson(json['settings'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$UserProfileToJson(UserProfile instance) =>
    <String, dynamic>{
      'id': instance.id,
      'nickname': instance.nickname,
      'avatarId': instance.avatarId,
      'createdAt': instance.createdAt.toIso8601String(),
      'lastPlayedAt': instance.lastPlayedAt.toIso8601String(),
      'levelProgress': instance.levelProgress,
      'unlockedAchievements': instance.unlockedAchievements,
      'totalPlayTimeMinutes': instance.totalPlayTimeMinutes,
      'skillPoints': instance.skillPoints,
      'totalPoints': instance.totalPoints,
      'totalScore': instance.totalScore,
      'settings': instance.settings,
    };

LevelProgress _$LevelProgressFromJson(Map<String, dynamic> json) =>
    LevelProgress(
      levelId: json['levelId'] as String,
      bestScore: (json['bestScore'] as num).toInt(),
      attempts: (json['attempts'] as num).toInt(),
      completed: json['completed'] as bool,
      firstCompletedAt: json['firstCompletedAt'] == null
          ? null
          : DateTime.parse(json['firstCompletedAt'] as String),
      lastAttemptAt: json['lastAttemptAt'] == null
          ? null
          : DateTime.parse(json['lastAttemptAt'] as String),
      bestTimeSeconds: (json['bestTimeSeconds'] as num).toInt(),
      hintsUsed: (json['hintsUsed'] as num).toInt(),
      starRating: (json['starRating'] as num).toInt(),
    );

Map<String, dynamic> _$LevelProgressToJson(LevelProgress instance) =>
    <String, dynamic>{
      'levelId': instance.levelId,
      'bestScore': instance.bestScore,
      'attempts': instance.attempts,
      'completed': instance.completed,
      'firstCompletedAt': instance.firstCompletedAt?.toIso8601String(),
      'lastAttemptAt': instance.lastAttemptAt?.toIso8601String(),
      'bestTimeSeconds': instance.bestTimeSeconds,
      'hintsUsed': instance.hintsUsed,
      'starRating': instance.starRating,
    };

UserSettings _$UserSettingsFromJson(Map<String, dynamic> json) => UserSettings(
      musicVolume: (json['musicVolume'] as num).toDouble(),
      sfxVolume: (json['sfxVolume'] as num).toDouble(),
      enableVibration: json['enableVibration'] as bool,
      enableAnimations: json['enableAnimations'] as bool,
      language: json['language'] as String,
      dailyTimeLimitMinutes: (json['dailyTimeLimitMinutes'] as num).toInt(),
      disabledTimeSlots: (json['disabledTimeSlots'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$UserSettingsToJson(UserSettings instance) =>
    <String, dynamic>{
      'musicVolume': instance.musicVolume,
      'sfxVolume': instance.sfxVolume,
      'enableVibration': instance.enableVibration,
      'enableAnimations': instance.enableAnimations,
      'language': instance.language,
      'dailyTimeLimitMinutes': instance.dailyTimeLimitMinutes,
      'disabledTimeSlots': instance.disabledTimeSlots,
    };
