import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import '../../domain/entities/user_profile_entity.dart';

part 'user_profile.g.dart';

/// 用户档案数据模型
@HiveType(typeId: 0)
@JsonSerializable()
// ignore: must_be_immutable
class UserProfile extends HiveObject with EquatableMixin {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String nickname;

  @HiveField(2)
  final String avatarId;

  @HiveField(3)
  final DateTime createdAt;

  @HiveField(4)
  final DateTime lastPlayedAt;

  @HiveField(5)
  final Map<String, LevelProgress> levelProgress;

  @HiveField(6)
  final List<String> unlockedAchievements;

  @HiveField(7)
  final int totalPlayTimeMinutes;

  @HiveField(8)
  final Map<String, int> skillPoints;

  @HiveField(9)
  final int totalPoints;

  @HiveField(10)
  final int totalScore;

  @HiveField(11)
  final UserSettings settings;

  UserProfile({
    required this.id,
    required this.nickname,
    required this.avatarId,
    required this.createdAt,
    required this.lastPlayedAt,
    required this.levelProgress,
    required this.unlockedAchievements,
    required this.totalPlayTimeMinutes,
    required this.skillPoints,
    required this.totalPoints,
    required this.totalScore,
    required this.settings,
  });

  /// 从JSON创建用户档案
  factory UserProfile.fromJson(Map<String, dynamic> json) =>
      _$UserProfileFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$UserProfileToJson(this);

  /// 创建新用户档案
  factory UserProfile.create({
    required String nickname,
    required String avatarId,
  }) {
    final now = DateTime.now();
    return UserProfile(
      id: '${now.millisecondsSinceEpoch}_${nickname.hashCode}',
      nickname: nickname,
      avatarId: avatarId,
      createdAt: now,
      lastPlayedAt: now,
      levelProgress: {},
      unlockedAchievements: [],
      totalPlayTimeMinutes: 0,
      skillPoints: {'pattern': 0, 'spatial': 0, 'numeric': 0, 'coding': 0},
      totalPoints: 0,
      totalScore: 0,
      settings: UserSettings.defaultSettings(),
    );
  }

  /// 复制并更新用户档案
  UserProfile copyWith({
    String? nickname,
    String? avatarId,
    DateTime? lastPlayedAt,
    Map<String, LevelProgress>? levelProgress,
    List<String>? unlockedAchievements,
    int? totalPlayTimeMinutes,
    Map<String, int>? skillPoints,
    int? totalPoints,
    int? totalScore,
    UserSettings? settings,
  }) {
    return UserProfile(
      id: id,
      nickname: nickname ?? this.nickname,
      avatarId: avatarId ?? this.avatarId,
      createdAt: createdAt,
      lastPlayedAt: lastPlayedAt ?? this.lastPlayedAt,
      levelProgress: levelProgress ?? Map.from(this.levelProgress),
      unlockedAchievements:
          unlockedAchievements ?? List.from(this.unlockedAchievements),
      totalPlayTimeMinutes: totalPlayTimeMinutes ?? this.totalPlayTimeMinutes,
      skillPoints: skillPoints ?? Map.from(this.skillPoints),
      totalPoints: totalPoints ?? this.totalPoints,
      totalScore: totalScore ?? this.totalScore,
      settings: settings ?? this.settings,
    );
  }

  /// 获取总星星数
  int get totalStars {
    return levelProgress.values
        .map((progress) => progress.bestScore)
        .fold(0, (sum, stars) => sum + stars);
  }

  /// 获取完成的关卡数
  int get completedLevels {
    return levelProgress.values.where((progress) => progress.completed).length;
  }

  /// 获取特定技能的等级
  int getSkillLevel(String skillType) {
    final points = skillPoints[skillType] ?? 0;
    // 每100点升一级
    return (points / 100).floor() + 1;
  }

  /// 转换为Domain实体
  UserProfileEntity toDomain() {
    return UserProfileEntity(
      id: id,
      nickname: nickname,
      avatarId: avatarId,
      createdAt: createdAt,
      lastPlayedAt: lastPlayedAt,
      levelProgress: levelProgress.map(
        (key, value) => MapEntry(key, value.toDomain()),
      ),
      unlockedAchievements: List.from(unlockedAchievements),
      totalPlayTimeMinutes: totalPlayTimeMinutes,
      skillPoints: Map.from(skillPoints),
      totalPoints: totalPoints,
      totalScore: totalPoints, // 使用totalPoints作为totalScore的值
      settings: settings.toDomain(),
    );
  }

  /// 从Domain实体创建
  factory UserProfile.fromDomain(UserProfileEntity entity) {
    return UserProfile(
      id: entity.id,
      nickname: entity.nickname,
      avatarId: entity.avatarId,
      createdAt: entity.createdAt,
      lastPlayedAt: entity.lastPlayedAt,
      levelProgress: entity.levelProgress.map(
        (key, value) => MapEntry(key, LevelProgress.fromDomain(value)),
      ),
      unlockedAchievements: List.from(entity.unlockedAchievements),
      totalPlayTimeMinutes: entity.totalPlayTimeMinutes,
      skillPoints: Map.from(entity.skillPoints),
      totalPoints: entity.totalPoints,
      totalScore: entity.totalScore,
      settings: UserSettings.fromDomain(entity.settings),
    );
  }

  @override
  List<Object?> get props => [
    id,
    nickname,
    avatarId,
    createdAt,
    lastPlayedAt,
    levelProgress,
    unlockedAchievements,
    totalPlayTimeMinutes,
    skillPoints,
    totalPoints,
    settings,
  ];
}

/// 关卡进度数据模型
@HiveType(typeId: 2)
@JsonSerializable()
// ignore: must_be_immutable
class LevelProgress extends HiveObject with EquatableMixin {
  @HiveField(0)
  final String levelId;

  @HiveField(1)
  final int bestScore; // 最高星数 (0-3)

  @HiveField(2)
  final int attempts;

  @HiveField(3)
  final bool completed;

  @HiveField(4)
  final DateTime? firstCompletedAt;

  @HiveField(5)
  final DateTime? lastAttemptAt;

  @HiveField(6)
  final int bestTimeSeconds;

  @HiveField(7)
  final int hintsUsed;

  @HiveField(8)
  final int starRating;

  LevelProgress({
    required this.levelId,
    required this.bestScore,
    required this.attempts,
    required this.completed,
    this.firstCompletedAt,
    this.lastAttemptAt,
    required this.bestTimeSeconds,
    required this.hintsUsed,
    required this.starRating,
  });

  /// 从JSON创建关卡进度
  factory LevelProgress.fromJson(Map<String, dynamic> json) =>
      _$LevelProgressFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$LevelProgressToJson(this);

  /// 创建新的关卡进度
  factory LevelProgress.create(String levelId) {
    return LevelProgress(
      levelId: levelId,
      bestScore: 0,
      attempts: 0,
      completed: false,
      bestTimeSeconds: 0,
      hintsUsed: 0,
      starRating: 0,
    );
  }

  /// 更新关卡进度
  LevelProgress updateProgress({
    required int score,
    required int timeSeconds,
    required int hintsUsed,
    int? starRating,
  }) {
    final now = DateTime.now();
    final isFirstCompletion = !completed && score > 0;
    final newStarRating = starRating ?? this.starRating;

    return LevelProgress(
      levelId: levelId,
      bestScore: score > bestScore ? score : bestScore,
      attempts: attempts + 1,
      completed: completed || score > 0,
      firstCompletedAt: isFirstCompletion ? now : firstCompletedAt,
      lastAttemptAt: now,
      bestTimeSeconds:
          bestTimeSeconds == 0 ||
              (timeSeconds > 0 && timeSeconds < bestTimeSeconds)
          ? timeSeconds
          : bestTimeSeconds,
      hintsUsed: this.hintsUsed + hintsUsed,
      starRating: newStarRating > this.starRating
          ? newStarRating
          : this.starRating,
    );
  }

  /// 转换为Domain实体
  LevelProgressEntity toDomain() {
    return LevelProgressEntity(
      levelId: levelId,
      bestScore: bestScore,
      attempts: attempts,
      completed: completed,
      firstCompletedAt: firstCompletedAt,
      lastAttemptAt: lastAttemptAt,
      bestTimeSeconds: bestTimeSeconds,
      hintsUsed: hintsUsed,
      starRating: starRating,
    );
  }

  /// 从Domain实体创建
  factory LevelProgress.fromDomain(LevelProgressEntity entity) {
    return LevelProgress(
      levelId: entity.levelId,
      bestScore: entity.bestScore,
      attempts: entity.attempts,
      completed: entity.completed,
      firstCompletedAt: entity.firstCompletedAt,
      lastAttemptAt: entity.lastAttemptAt,
      bestTimeSeconds: entity.bestTimeSeconds,
      hintsUsed: entity.hintsUsed,
      starRating: entity.starRating,
    );
  }

  @override
  List<Object?> get props => [
    levelId,
    bestScore,
    attempts,
    completed,
    firstCompletedAt,
    lastAttemptAt,
    bestTimeSeconds,
    hintsUsed,
    starRating,
  ];
}

/// 用户设置数据模型
@HiveType(typeId: 2)
@JsonSerializable()
// ignore: must_be_immutable
class UserSettings extends HiveObject with EquatableMixin {
  @HiveField(0)
  final double musicVolume;

  @HiveField(1)
  final double sfxVolume;

  @HiveField(2)
  final bool enableVibration;

  @HiveField(3)
  final bool enableAnimations;

  @HiveField(4)
  final String language;

  @HiveField(5)
  final int dailyTimeLimitMinutes;

  @HiveField(6)
  final List<String> disabledTimeSlots;

  UserSettings({
    required this.musicVolume,
    required this.sfxVolume,
    required this.enableVibration,
    required this.enableAnimations,
    required this.language,
    required this.dailyTimeLimitMinutes,
    required this.disabledTimeSlots,
  });

  /// 从JSON创建用户设置
  factory UserSettings.fromJson(Map<String, dynamic> json) =>
      _$UserSettingsFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$UserSettingsToJson(this);

  /// 默认设置
  factory UserSettings.defaultSettings() {
    return UserSettings(
      musicVolume: 0.7,
      sfxVolume: 0.8,
      enableVibration: true,
      enableAnimations: true,
      language: 'zh_CN',
      dailyTimeLimitMinutes: 60,
      disabledTimeSlots: const [],
    );
  }

  /// 复制并更新设置
  UserSettings copyWith({
    double? musicVolume,
    double? sfxVolume,
    bool? enableVibration,
    bool? enableAnimations,
    String? language,
    int? dailyTimeLimitMinutes,
    List<String>? disabledTimeSlots,
  }) {
    return UserSettings(
      musicVolume: musicVolume ?? this.musicVolume,
      sfxVolume: sfxVolume ?? this.sfxVolume,
      enableVibration: enableVibration ?? this.enableVibration,
      enableAnimations: enableAnimations ?? this.enableAnimations,
      language: language ?? this.language,
      dailyTimeLimitMinutes:
          dailyTimeLimitMinutes ?? this.dailyTimeLimitMinutes,
      disabledTimeSlots: disabledTimeSlots ?? List.from(this.disabledTimeSlots),
    );
  }

  /// 转换为Domain实体
  UserSettingsEntity toDomain() {
    return UserSettingsEntity(
      musicVolume: musicVolume,
      sfxVolume: sfxVolume,
      enableVibration: enableVibration,
      enableAnimations: enableAnimations,
      language: language,
      dailyTimeLimitMinutes: dailyTimeLimitMinutes,
      disabledTimeSlots: List.from(disabledTimeSlots),
    );
  }

  /// 从Domain实体创建
  factory UserSettings.fromDomain(UserSettingsEntity entity) {
    return UserSettings(
      musicVolume: entity.musicVolume,
      sfxVolume: entity.sfxVolume,
      enableVibration: entity.enableVibration,
      enableAnimations: entity.enableAnimations,
      language: entity.language,
      dailyTimeLimitMinutes: entity.dailyTimeLimitMinutes,
      disabledTimeSlots: List.from(entity.disabledTimeSlots),
    );
  }

  @override
  List<Object?> get props => [
    musicVolume,
    sfxVolume,
    enableVibration,
    enableAnimations,
    language,
    dailyTimeLimitMinutes,
    disabledTimeSlots,
  ];
}
